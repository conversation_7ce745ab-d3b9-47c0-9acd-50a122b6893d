1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.craftworks.music"
4    android:versionCode="271"
5    android:versionName="1.27.1" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:5:5-67
11-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
12-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:6:5-77
12-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
13-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:7:5-75
13-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:7:22-72
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:8:5-109
14-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:8:22-77
15    <uses-permission android:name="android.permission.WAKE_LOCK" />
15-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:9:5-68
15-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:9:22-65
16
17    <!-- Android Auto -->
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
18-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:12:5-77
18-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:12:22-74
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
19-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:13:5-92
19-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:13:22-89
20
21    <uses-feature
21-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:15:5-17:36
22        android:name="android.hardware.wifi"
22-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:16:9-45
23        android:required="false" />
23-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:17:9-33
24    <uses-feature
24-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:18:5-20:36
25        android:name="android.software.leanback"
25-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:19:9-49
26        android:required="false" />
26-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:20:9-33
27    <uses-feature
27-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:21:5-23:36
28        android:name="android.hardware.touchscreen"
28-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:22:9-52
29        android:required="false" />
29-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:23:9-33
30
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[androidx.media3:media3-exoplayer:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\114a5d5301d7649ab21683cbabeecc89\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:22:5-79
31-->[androidx.media3:media3-exoplayer:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\114a5d5301d7649ab21683cbabeecc89\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:22:22-76
32
33    <permission
33-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
34        android:name="com.craftworks.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
34-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
35        android:protectionLevel="signature" />
35-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
36
37    <uses-permission android:name="com.craftworks.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
37-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
37-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
38
39    <application
39-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:25:5-81:19
40        android:allowBackup="true"
40-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:26:9-35
41        android:appCategory="audio"
41-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:36:9-36
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
43        android:banner="@mipmap/ic_banner"
43-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:27:9-43
44        android:dataExtractionRules="@xml/data_extraction_rules"
44-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:28:9-65
45        android:debuggable="true"
46        android:enableOnBackInvokedCallback="true"
46-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:37:9-51
47        android:extractNativeLibs="false"
48        android:fullBackupContent="@xml/backup_rules"
48-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:29:9-54
49        android:icon="@mipmap/ic_launcher"
49-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:30:9-43
50        android:label="@string/app_name"
50-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:31:9-41
51        android:localeConfig="@xml/_generated_res_locale_config"
52        android:roundIcon="@mipmap/ic_launcher"
52-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:32:9-48
53        android:supportsRtl="true"
53-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:33:9-35
54        android:testOnly="true"
55        android:theme="@style/Theme.MusicPlayer"
55-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:34:9-49
56        android:usesCleartextTraffic="true" >
56-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:35:9-44
57        <meta-data
57-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:42:9-43:58
58            android:name="com.google.android.gms.car.application"
58-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:42:20-73
59            android:resource="@xml/automotive_app_desc" />
59-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:43:13-56
60
61        <activity
61-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:45:9-60:20
62            android:name="com.craftworks.music.MainActivity"
62-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:46:13-41
63            android:configChanges="orientation|screenSize"
63-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:52:13-59
64            android:exported="true"
64-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:48:13-36
65            android:hardwareAccelerated="true"
65-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:50:13-47
66            android:launchMode="singleInstance"
66-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:47:13-48
67            android:theme="@style/Theme.MusicPlayer" >
67-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:49:13-53
68            <intent-filter>
68-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:55:13-59:29
69                <action android:name="android.intent.action.MAIN" />
69-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:56:17-69
69-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:56:25-66
70
71                <category android:name="android.intent.category.LAUNCHER" />
71-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:57:17-77
71-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:57:27-74
72                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
72-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:58:17-86
72-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:58:27-83
73            </intent-filter>
74        </activity>
75
76        <!-- Android Auto Media Library -->
77        <service
77-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:63:9-71:19
78            android:name="com.craftworks.music.player.ChoraMediaLibraryService"
78-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:64:13-60
79            android:exported="true"
79-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:66:13-36
80            android:foregroundServiceType="mediaPlayback"
80-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:65:13-58
81            android:permission="android.permission.FOREGROUND_SERVICE" >
81-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:67:13-71
82            <intent-filter>
82-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:68:13-70:29
83                <action android:name="android.media.browse.MediaBrowserService" />
83-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:69:17-82
83-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:69:25-80
84            </intent-filter>
85        </service>
86
87        <!-- Playback Resumption Button Receiver -->
88        <receiver
88-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:74:9-79:20
89            android:name="androidx.media3.session.MediaButtonReceiver"
89-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:74:19-77
90            android:exported="true" >
90-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:75:13-36
91            <intent-filter>
91-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:76:13-78:29
92                <action android:name="android.intent.action.MEDIA_BUTTON" />
92-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:77:17-77
92-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:77:25-74
93            </intent-filter>
94        </receiver>
95
96        <activity
96-->[androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
97            android:name="androidx.compose.ui.tooling.PreviewActivity"
97-->[androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
98            android:exported="true" />
98-->[androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
99
100        <uses-library
100-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
101            android:name="androidx.window.extensions"
101-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
102            android:required="false" />
102-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
103        <uses-library
103-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
104            android:name="androidx.window.sidecar"
104-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
105            android:required="false" />
105-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
106
107        <provider
107-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
108            android:name="androidx.startup.InitializationProvider"
108-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
109            android:authorities="com.craftworks.music.androidx-startup"
109-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
110            android:exported="false" >
110-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
111            <meta-data
111-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
112                android:name="androidx.emoji2.text.EmojiCompatInitializer"
112-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
113                android:value="androidx.startup" />
113-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
114            <meta-data
114-->[androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
115                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
115-->[androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
116                android:value="androidx.startup" />
116-->[androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
117            <meta-data
117-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
118                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
118-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
119                android:value="androidx.startup" />
119-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
120        </provider>
121
122        <activity
122-->[androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:23:9-26:79
123            android:name="androidx.activity.ComponentActivity"
123-->[androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:24:13-63
124            android:exported="true"
124-->[androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:25:13-36
125            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
125-->[androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:26:13-76
126
127        <receiver
127-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
128            android:name="androidx.profileinstaller.ProfileInstallReceiver"
128-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
129            android:directBootAware="false"
129-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
130            android:enabled="true"
130-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
131            android:exported="true"
131-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
132            android:permission="android.permission.DUMP" >
132-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
133            <intent-filter>
133-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
134                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
134-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
134-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
135            </intent-filter>
136            <intent-filter>
136-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
137                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
137-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
137-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
138            </intent-filter>
139            <intent-filter>
139-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
140                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
140-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
140-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
141            </intent-filter>
142            <intent-filter>
142-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
143                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
143-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
143-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
144            </intent-filter>
145        </receiver>
146    </application>
147
148</manifest>
