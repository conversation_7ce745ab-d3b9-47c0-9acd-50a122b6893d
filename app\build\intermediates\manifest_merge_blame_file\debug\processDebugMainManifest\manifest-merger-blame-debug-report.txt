1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.craftworks.music"
4    android:versionCode="271"
5    android:versionName="1.27.1" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:5:5-67
11-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
12-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:6:5-77
12-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
13-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:7:5-75
13-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:7:22-72
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:8:5-109
14-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:8:22-77
15    <uses-permission android:name="android.permission.WAKE_LOCK" />
15-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:9:5-68
15-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:9:22-65
16
17    <!-- Android Auto -->
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
18-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:12:5-77
18-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:12:22-74
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
19-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:13:5-92
19-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:13:22-89
20
21    <uses-feature
21-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:15:5-17:36
22        android:name="android.hardware.wifi"
22-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:16:9-45
23        android:required="false" />
23-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:17:9-33
24    <uses-feature
24-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:18:5-20:36
25        android:name="android.software.leanback"
25-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:19:9-49
26        android:required="false" />
26-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:20:9-33
27    <uses-feature
27-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:21:5-23:36
28        android:name="android.hardware.touchscreen"
28-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:22:9-52
29        android:required="false" />
29-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:23:9-33
30
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[androidx.media3:media3-exoplayer:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\114a5d5301d7649ab21683cbabeecc89\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:22:5-79
31-->[androidx.media3:media3-exoplayer:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\114a5d5301d7649ab21683cbabeecc89\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:22:22-76
32
33    <permission
33-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
34        android:name="com.craftworks.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
34-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
35        android:protectionLevel="signature" />
35-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
36
37    <uses-permission android:name="com.craftworks.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
37-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
37-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
38
39    <application
39-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:25:5-81:19
40        android:allowBackup="true"
40-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:26:9-35
41        android:appCategory="audio"
41-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:36:9-36
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
43        android:banner="@mipmap/ic_banner"
43-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:27:9-43
44        android:dataExtractionRules="@xml/data_extraction_rules"
44-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:28:9-65
45        android:debuggable="true"
46        android:enableOnBackInvokedCallback="true"
46-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:37:9-51
47        android:extractNativeLibs="false"
48        android:fullBackupContent="@xml/backup_rules"
48-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:29:9-54
49        android:icon="@mipmap/ic_launcher"
49-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:30:9-43
50        android:label="@string/app_name"
50-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:31:9-41
51        android:localeConfig="@xml/_generated_res_locale_config"
52        android:roundIcon="@mipmap/ic_launcher"
52-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:32:9-48
53        android:supportsRtl="true"
53-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:33:9-35
54        android:theme="@style/Theme.MusicPlayer"
54-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:34:9-49
55        android:usesCleartextTraffic="true" >
55-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:35:9-44
56        <meta-data
56-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:42:9-43:58
57            android:name="com.google.android.gms.car.application"
57-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:42:20-73
58            android:resource="@xml/automotive_app_desc" />
58-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:43:13-56
59
60        <activity
60-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:45:9-60:20
61            android:name="com.craftworks.music.MainActivity"
61-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:46:13-41
62            android:configChanges="orientation|screenSize"
62-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:52:13-59
63            android:exported="true"
63-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:48:13-36
64            android:hardwareAccelerated="true"
64-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:50:13-47
65            android:launchMode="singleInstance"
65-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:47:13-48
66            android:theme="@style/Theme.MusicPlayer" >
66-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:49:13-53
67            <intent-filter>
67-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:55:13-59:29
68                <action android:name="android.intent.action.MAIN" />
68-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:56:17-69
68-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:56:25-66
69
70                <category android:name="android.intent.category.LAUNCHER" />
70-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:57:17-77
70-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:57:27-74
71                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
71-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:58:17-86
71-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:58:27-83
72            </intent-filter>
73        </activity>
74
75        <!-- Android Auto Media Library -->
76        <service
76-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:63:9-71:19
77            android:name="com.craftworks.music.player.ChoraMediaLibraryService"
77-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:64:13-60
78            android:exported="true"
78-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:66:13-36
79            android:foregroundServiceType="mediaPlayback"
79-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:65:13-58
80            android:permission="android.permission.FOREGROUND_SERVICE" >
80-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:67:13-71
81            <intent-filter>
81-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:68:13-70:29
82                <action android:name="android.media.browse.MediaBrowserService" />
82-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:69:17-82
82-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:69:25-80
83            </intent-filter>
84        </service>
85
86        <!-- Playback Resumption Button Receiver -->
87        <receiver
87-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:74:9-79:20
88            android:name="androidx.media3.session.MediaButtonReceiver"
88-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:74:19-77
89            android:exported="true" >
89-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:75:13-36
90            <intent-filter>
90-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:76:13-78:29
91                <action android:name="android.intent.action.MEDIA_BUTTON" />
91-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:77:17-77
91-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:77:25-74
92            </intent-filter>
93        </receiver>
94
95        <activity
95-->[androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
96            android:name="androidx.compose.ui.tooling.PreviewActivity"
96-->[androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
97            android:exported="true" />
97-->[androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
98
99        <uses-library
99-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
100            android:name="androidx.window.extensions"
100-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
101            android:required="false" />
101-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
102        <uses-library
102-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
103            android:name="androidx.window.sidecar"
103-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
104            android:required="false" />
104-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
105
106        <provider
106-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
107            android:name="androidx.startup.InitializationProvider"
107-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
108            android:authorities="com.craftworks.music.androidx-startup"
108-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
109            android:exported="false" >
109-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
110            <meta-data
110-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
111                android:name="androidx.emoji2.text.EmojiCompatInitializer"
111-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
112                android:value="androidx.startup" />
112-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
113            <meta-data
113-->[androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
114                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
114-->[androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
115                android:value="androidx.startup" />
115-->[androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
116            <meta-data
116-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
117                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
117-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
118                android:value="androidx.startup" />
118-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
119        </provider>
120
121        <activity
121-->[androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:23:9-26:79
122            android:name="androidx.activity.ComponentActivity"
122-->[androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:24:13-63
123            android:exported="true"
123-->[androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:25:13-36
124            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
124-->[androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:26:13-76
125
126        <receiver
126-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
127            android:name="androidx.profileinstaller.ProfileInstallReceiver"
127-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
128            android:directBootAware="false"
128-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
129            android:enabled="true"
129-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
130            android:exported="true"
130-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
131            android:permission="android.permission.DUMP" >
131-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
132            <intent-filter>
132-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
133                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
133-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
133-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
134            </intent-filter>
135            <intent-filter>
135-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
136                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
136-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
136-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
137            </intent-filter>
138            <intent-filter>
138-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
139                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
139-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
139-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
140            </intent-filter>
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
142                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
142-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
142-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
143            </intent-filter>
144        </receiver>
145    </application>
146
147</manifest>
