1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.craftworks.music"
4    android:versionCode="271"
5    android:versionName="1.27.1" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:5:5-67
11-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
12-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:6:5-77
12-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
13-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:7:5-75
13-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:7:22-72
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:8:5-109
14-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:8:22-77
15    <uses-permission
15-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:9:5-11:40
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:9:22-78
17        android:maxSdkVersion="28" />
17-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:10:9-35
18    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
18-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:12:5-13:40
18-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:12:22-79
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:14:5-68
19-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:14:22-65
20
21    <!-- Android Auto -->
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
22-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:17:5-77
22-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:17:22-74
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
23-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:18:5-92
23-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:18:22-89
24
25    <uses-feature
25-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:20:5-22:36
26        android:name="android.hardware.wifi"
26-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:21:9-45
27        android:required="false" />
27-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:22:9-33
28    <uses-feature
28-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:23:5-25:36
29        android:name="android.software.leanback"
29-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:24:9-49
30        android:required="false" />
30-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:25:9-33
31    <uses-feature
31-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:26:5-28:36
32        android:name="android.hardware.touchscreen"
32-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:27:9-52
33        android:required="false" />
33-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:28:9-33
34
35    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
35-->[androidx.media3:media3-exoplayer:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\114a5d5301d7649ab21683cbabeecc89\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:22:5-79
35-->[androidx.media3:media3-exoplayer:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\114a5d5301d7649ab21683cbabeecc89\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:22:22-76
36
37    <permission
37-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
38        android:name="com.craftworks.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.craftworks.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
42
43    <application
43-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:30:5-99:19
44        android:allowBackup="true"
44-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:31:9-35
45        android:appCategory="audio"
45-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:41:9-36
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
47        android:banner="@mipmap/ic_banner"
47-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:32:9-43
48        android:dataExtractionRules="@xml/data_extraction_rules"
48-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:33:9-65
49        android:debuggable="true"
50        android:enableOnBackInvokedCallback="true"
50-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:42:9-51
51        android:extractNativeLibs="false"
52        android:fullBackupContent="@xml/backup_rules"
52-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:34:9-54
53        android:icon="@mipmap/ic_launcher"
53-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:35:9-43
54        android:label="@string/app_name"
54-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:36:9-41
55        android:localeConfig="@xml/_generated_res_locale_config"
56        android:roundIcon="@mipmap/ic_launcher"
56-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:37:9-48
57        android:supportsRtl="true"
57-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:38:9-35
58        android:theme="@style/Theme.MusicPlayer"
58-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:39:9-49
59        android:usesCleartextTraffic="true" >
59-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:40:9-44
60        <meta-data
60-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:47:9-48:58
61            android:name="com.google.android.gms.car.application"
61-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:47:20-73
62            android:resource="@xml/automotive_app_desc" />
62-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:48:13-56
63
64        <activity
64-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:50:9-65:20
65            android:name="com.craftworks.music.MainActivity"
65-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:51:13-41
66            android:configChanges="orientation|screenSize"
66-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:57:13-59
67            android:exported="true"
67-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:53:13-36
68            android:hardwareAccelerated="true"
68-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:55:13-47
69            android:launchMode="singleInstance"
69-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:52:13-48
70            android:theme="@style/Theme.MusicPlayer" >
70-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:54:13-53
71            <intent-filter>
71-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:60:13-64:29
72                <action android:name="android.intent.action.MAIN" />
72-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:61:17-69
72-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:61:25-66
73
74                <category android:name="android.intent.category.LAUNCHER" />
74-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:62:17-77
74-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:62:27-74
75                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
75-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:63:17-86
75-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:63:27-83
76            </intent-filter>
77        </activity>
78
79        <!-- Android Auto Media Library -->
80        <service
80-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:68:9-76:19
81            android:name="com.craftworks.music.player.ChoraMediaLibraryService"
81-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:69:13-60
82            android:exported="true"
82-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:71:13-36
83            android:foregroundServiceType="mediaPlayback"
83-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:70:13-58
84            android:permission="android.permission.FOREGROUND_SERVICE" >
84-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:72:13-71
85            <intent-filter>
85-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:73:13-75:29
86                <action android:name="android.media.browse.MediaBrowserService" />
86-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:74:17-82
86-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:74:25-80
87            </intent-filter>
88        </service>
89
90        <!-- Playback Resumption Button Receiver -->
91        <receiver
91-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:79:9-84:20
92            android:name="androidx.media3.session.MediaButtonReceiver"
92-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:79:19-77
93            android:exported="true" >
93-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:80:13-36
94            <intent-filter>
94-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:81:13-83:29
95                <action android:name="android.intent.action.MEDIA_BUTTON" />
95-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:82:17-77
95-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:82:25-74
96            </intent-filter>
97        </receiver>
98
99        <!-- Music Widget -->
100        <receiver
100-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:87:9-97:20
101            android:name="com.craftworks.music.widget.MusicWidgetProvider"
101-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:87:19-61
102            android:exported="true" >
102-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:88:13-36
103            <intent-filter>
103-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:89:13-94:29
104                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
104-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:90:17-84
104-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:90:25-81
105                <action android:name="com.craftworks.music.widget.PLAY_PAUSE" />
105-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:91:17-81
105-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:91:25-78
106                <action android:name="com.craftworks.music.widget.NEXT" />
106-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:92:17-75
106-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:92:25-72
107                <action android:name="com.craftworks.music.widget.PREVIOUS" />
107-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:93:17-79
107-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:93:25-76
108            </intent-filter>
109
110            <meta-data
110-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:95:13-96:61
111                android:name="android.appwidget.provider"
111-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:95:24-65
112                android:resource="@xml/music_widget_info" />
112-->E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:96:17-58
113        </receiver>
114
115        <activity
115-->[androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
116            android:name="androidx.compose.ui.tooling.PreviewActivity"
116-->[androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
117            android:exported="true" />
117-->[androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
118
119        <uses-library
119-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
120            android:name="androidx.window.extensions"
120-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
121            android:required="false" />
121-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
122        <uses-library
122-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
123            android:name="androidx.window.sidecar"
123-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
124            android:required="false" />
124-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
125
126        <provider
126-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
127            android:name="androidx.startup.InitializationProvider"
127-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
128            android:authorities="com.craftworks.music.androidx-startup"
128-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
129            android:exported="false" >
129-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
130            <meta-data
130-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
131                android:name="androidx.emoji2.text.EmojiCompatInitializer"
131-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
132                android:value="androidx.startup" />
132-->[androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
133            <meta-data
133-->[androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
134                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
134-->[androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
135                android:value="androidx.startup" />
135-->[androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
136            <meta-data
136-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
137                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
137-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
138                android:value="androidx.startup" />
138-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
139        </provider>
140
141        <activity
141-->[androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:23:9-26:79
142            android:name="androidx.activity.ComponentActivity"
142-->[androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:24:13-63
143            android:exported="true"
143-->[androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:25:13-36
144            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
144-->[androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:26:13-76
145
146        <receiver
146-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
147            android:name="androidx.profileinstaller.ProfileInstallReceiver"
147-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
148            android:directBootAware="false"
148-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
149            android:enabled="true"
149-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
150            android:exported="true"
150-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
151            android:permission="android.permission.DUMP" >
151-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
152            <intent-filter>
152-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
153                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
153-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
153-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
154            </intent-filter>
155            <intent-filter>
155-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
156                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
156-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
156-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
157            </intent-filter>
158            <intent-filter>
158-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
159                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
159-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
159-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
160            </intent-filter>
161            <intent-filter>
161-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
162                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
162-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
162-->[androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
163            </intent-filter>
164        </receiver>
165    </application>
166
167</manifest>
