package com.craftworks.music.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.drawable.BitmapDrawable
import android.widget.RemoteViews
import androidx.core.content.ContextCompat
import androidx.media3.common.MediaMetadata
import androidx.media3.common.Player
import com.craftworks.music.MainActivity
import com.craftworks.music.R
import com.craftworks.music.player.SongHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.InputStream
import java.net.URL

class MusicWidgetProvider : AppWidgetProvider() {

    companion object {
        const val ACTION_PLAY_PAUSE = "com.craftworks.music.widget.PLAY_PAUSE"
        const val ACTION_NEXT = "com.craftworks.music.widget.NEXT"
        const val ACTION_PREVIOUS = "com.craftworks.music.widget.PREVIOUS"
        
        fun updateWidget(context: Context, isPlaying: Boolean, metadata: MediaMetadata?) {
            val appWidgetManager = AppWidgetManager.getInstance(context)
            val widgetComponent = ComponentName(context, MusicWidgetProvider::class.java)
            val widgetIds = appWidgetManager.getAppWidgetIds(widgetComponent)
            
            for (widgetId in widgetIds) {
                updateAppWidget(context, appWidgetManager, widgetId, isPlaying, metadata)
            }
        }
        
        private fun updateAppWidget(
            context: Context,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int,
            isPlaying: Boolean,
            metadata: MediaMetadata?
        ) {
            val views = RemoteViews(context.packageName, R.layout.music_widget)
            
            // Update song info
            val title = metadata?.title?.toString() ?: "No song playing"
            val artist = metadata?.artist?.toString() ?: "Unknown Artist"
            
            views.setTextViewText(R.id.widget_song_title, title)
            views.setTextViewText(R.id.widget_artist_name, artist)
            
            // Update play/pause button
            val playPauseIcon = if (isPlaying) R.drawable.ic_pause else R.drawable.ic_play_arrow
            views.setImageViewResource(R.id.widget_play_pause, playPauseIcon)
            
            // Set up click intents
            setupClickIntents(context, views)
            
            // Update album art
            updateAlbumArt(context, views, metadata)
            
            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
        
        private fun setupClickIntents(context: Context, views: RemoteViews) {
            // Play/Pause button
            val playPauseIntent = Intent(context, MusicWidgetProvider::class.java).apply {
                action = ACTION_PLAY_PAUSE
            }
            val playPausePendingIntent = PendingIntent.getBroadcast(
                context, 0, playPauseIntent, 
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_play_pause, playPausePendingIntent)
            
            // Previous button
            val previousIntent = Intent(context, MusicWidgetProvider::class.java).apply {
                action = ACTION_PREVIOUS
            }
            val previousPendingIntent = PendingIntent.getBroadcast(
                context, 1, previousIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_previous, previousPendingIntent)
            
            // Next button
            val nextIntent = Intent(context, MusicWidgetProvider::class.java).apply {
                action = ACTION_NEXT
            }
            val nextPendingIntent = PendingIntent.getBroadcast(
                context, 2, nextIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_next, nextPendingIntent)
            
            // Widget click to open app
            val openAppIntent = Intent(context, MainActivity::class.java)
            val openAppPendingIntent = PendingIntent.getActivity(
                context, 3, openAppIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_album_art, openAppPendingIntent)
        }
        
        private fun updateAlbumArt(context: Context, views: RemoteViews, metadata: MediaMetadata?) {
            val artworkUri = metadata?.artworkUri
            if (artworkUri != null) {
                CoroutineScope(Dispatchers.IO).launch {
                    try {
                        val inputStream: InputStream = URL(artworkUri.toString()).openConnection().getInputStream()
                        val bitmap = BitmapFactory.decodeStream(inputStream)
                        if (bitmap != null) {
                            views.setImageViewBitmap(R.id.widget_album_art, bitmap)
                        } else {
                            views.setImageViewResource(R.id.widget_album_art, R.drawable.ic_music_note)
                        }
                    } catch (e: Exception) {
                        views.setImageViewResource(R.id.widget_album_art, R.drawable.ic_music_note)
                    }
                }
            } else {
                views.setImageViewResource(R.id.widget_album_art, R.drawable.ic_music_note)
            }
        }
    }

    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId, false, null)
        }
    }

    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)
        
        when (intent.action) {
            ACTION_PLAY_PAUSE -> {
                SongHelper.currentMediaController?.let { controller ->
                    if (controller.isPlaying) {
                        controller.pause()
                    } else {
                        controller.play()
                    }
                }
            }
            ACTION_NEXT -> {
                SongHelper.currentMediaController?.seekToNext()
            }
            ACTION_PREVIOUS -> {
                SongHelper.currentMediaController?.seekToPrevious()
            }
        }
    }
}
