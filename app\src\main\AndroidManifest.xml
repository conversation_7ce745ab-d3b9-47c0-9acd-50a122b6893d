<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- Android Auto -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />

    <uses-feature
        android:name="android.hardware.wifi"
        android:required="false" />
    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <application
        android:allowBackup="true"
        android:banner="@mipmap/ic_banner"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/Theme.MusicPlayer"
        android:usesCleartextTraffic="true"
        android:appCategory="audio"
        android:enableOnBackInvokedCallback="true"

        tools:targetApi="35"
        tools:ignore="UnusedAttribute">

        <meta-data android:name="com.google.android.gms.car.application"
            android:resource="@xml/automotive_app_desc"/>

        <activity
            android:name=".MainActivity"
            android:launchMode="singleInstance"
            android:exported="true"
            android:theme="@style/Theme.MusicPlayer"
            android:hardwareAccelerated="true"

            android:configChanges="orientation|screenSize"

            tools:ignore="LockedOrientationActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Android Auto Media Library -->
        <service
            android:name=".player.ChoraMediaLibraryService"
            android:foregroundServiceType="mediaPlayback"
            android:exported="true"
            android:permission="android.permission.FOREGROUND_SERVICE">
            <intent-filter>
                <action android:name="android.media.browse.MediaBrowserService"/>
            </intent-filter>
        </service>

        <!-- Playback Resumption Button Receiver -->
        <receiver android:name="androidx.media3.session.MediaButtonReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_BUTTON" />
            </intent-filter>
        </receiver>

        <!-- Music Widget -->
        <receiver android:name=".widget.MusicWidgetProvider"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="com.craftworks.music.widget.PLAY_PAUSE" />
                <action android:name="com.craftworks.music.widget.NEXT" />
                <action android:name="com.craftworks.music.widget.PREVIOUS" />
            </intent-filter>
            <meta-data android:name="android.appwidget.provider"
                android:resource="@xml/music_widget_info" />
        </receiver>

    </application>
</manifest>