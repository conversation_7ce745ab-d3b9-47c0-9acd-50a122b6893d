<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:background="@drawable/widget_background"
    android:padding="8dp">

    <!-- Album Art -->
    <ImageView
        android:id="@+id/widget_album_art"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_gravity="center_vertical"
        android:scaleType="centerCrop"
        android:background="@drawable/widget_album_background"
        android:src="@drawable/ic_music_note" />

    <!-- Song Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="8dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/widget_song_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="No song playing"
            android:textColor="@color/widget_text_primary"
            android:textSize="14sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/widget_artist_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Unknown Artist"
            android:textColor="@color/widget_text_secondary"
            android:textSize="12sp"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginTop="2dp" />

    </LinearLayout>

    <!-- Control Buttons -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:orientation="horizontal">

        <ImageButton
            android:id="@+id/widget_previous"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="@drawable/widget_button_background"
            android:src="@drawable/ic_skip_previous"
            android:contentDescription="Previous"
            android:scaleType="centerInside"
            android:layout_marginEnd="4dp" />

        <ImageButton
            android:id="@+id/widget_play_pause"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/widget_button_background"
            android:src="@drawable/ic_play_arrow"
            android:contentDescription="Play/Pause"
            android:scaleType="centerInside"
            android:layout_marginEnd="4dp" />

        <ImageButton
            android:id="@+id/widget_next"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="@drawable/widget_button_background"
            android:src="@drawable/ic_skip_next"
            android:contentDescription="Next"
            android:scaleType="centerInside" />

    </LinearLayout>

</LinearLayout>
