<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="white">#FFFFFFFF</color>
    <string name="Action_Add">Add</string>
    <string name="Action_Back">Back</string>
    <string name="Action_Cancel">Cancel</string>
    <string name="Action_Confirm">Confirm</string>
    <string name="Action_CreatePlaylist">Create Playlist</string>
    <string name="Action_Done">Done</string>
    <string name="Action_Download">Download</string>
    <string name="Action_Exit">Exit</string>
    <string name="Action_Go">Let\'s Go!</string>
    <string name="Action_Login">Login</string>
    <string name="Action_Play">Play</string>
    <string name="Action_Remove">Remove</string>
    <string name="Action_Reset">Reset</string>
    <string name="Action_Search">Search</string>
    <string name="Action_Select">Select</string>
    <string name="Action_Shuffle">Shuffle</string>
    <string name="Action_Success">Success</string>
    <string name="Albums">Albums</string>
    <string name="Artists">Artists</string>
    <string name="Background_Anim">Animated</string>
    <string name="Background_Blur">Blurred</string>
    <string name="Background_Plain">Plain</string>
    <string name="Dialog_Add_Radio">Add Internet Radio</string>
    <string name="Dialog_Add_To_Playlist">Add / To Playlist</string>
    <string name="Dialog_Background_Type">Background Style</string>
    <string name="Dialog_Delete_Playlist">Delete Playlist</string>
    <string name="Dialog_LocalMusicDirectories">Local Music Directories</string>
    <string name="Dialog_Media_Source">Media Source</string>
    <string name="Dialog_Modify_Radio">Modify </string>
    <string name="Dialog_New_Playlist">New Playlist</string>
    <string name="Dialog_SelectDirectory">Select Directory</string>
    <string name="Dialog_Theme">Theme</string>
    <string name="Dialog_Transcoding">Transcoding</string>
    <string name="Label_AddDirectory">Add Directory</string>
    <string name="Label_Allow_Self_Signed_Certs">Allow self-signed certs</string>
    <string name="Label_CacheCleared">Cache cleared</string>
    <string name="Label_CacheSize">Cache Size:</string>
    <string name="Label_Confirm_Delete_Playlist">Are you sure you want to delete this playlist?</string>
    <string name="Label_CurrentDirectory">Current Directory:</string>
    <string name="Label_Local_Directory">Directory:</string>
    <string name="Label_MoreSongsFrom">More Songs From</string>
    <string name="Label_Navidrome_Password">Password:</string>
    <string name="Label_Navidrome_URL">Navidrome URL:</string>
    <string name="Label_Navidrome_Username">Username:</string>
    <string name="Label_NoDescription">No Description Available.</string>
    <string name="Label_NoDirectories">No directories added</string>
    <string name="Label_Playlist_Name">Playlist Name:</string>
    <string name="Label_Radio_Add_To_Navidrome">Add to Navidrome</string>
    <string name="Label_Radio_Homepage">Radio Homepage URL:</string>
    <string name="Label_Radio_Name">Radio Name:</string>
    <string name="Label_Radio_URL">Radio URL:</string>
    <string name="Label_RemoveDirectory">Remove Directory</string>
    <string name="Label_SelectFolder">Select Folder</string>
    <string name="Label_Sorting">Sort By</string>
    <string name="Navidrome_Error">Navidrome server unreachable.</string>
    <string name="Navidrome_Sync">Navidrome sync in progress…</string>
    <string name="No_Providers_Splash">You haven\'t added a media provider yet. Add one now to start listening to some tunes!</string>
    <string name="Notification_Download_Desc">Notifications for download status</string>
    <string name="Notification_Download_Failure">Uh oh, something went wrong while downloading</string>
    <string name="Notification_Download_Name">Download Notifications</string>
    <string name="Notification_Download_Progress">Downloading</string>
    <string name="Notification_Download_Success">downloaded successfully!</string>
    <string name="Option_No_Transcoding">No Transcoding</string>
    <string name="Screen_Discography">Discography</string>
    <string name="Screen_Top_Songs">Top Songs</string>
    <string name="Setting_AutoCache">Auto Cache Songs</string>
    <string name="Setting_AutoScan">Auto Scan</string>
    <string name="Setting_Background">Background Style</string>
    <string name="Setting_CacheSize">Cache Size Limit</string>
    <string name="Setting_CacheUsage">Cache Usage</string>
    <string name="Setting_ClearCache">Clear Cache</string>
    <string name="Setting_LocalMusicDirectories">Local Music Directories</string>
    <string name="Setting_LyricsAnimationSpeed">Lyrics Animation Speed</string>
    <string name="Setting_ManualRescan">Manual Rescan</string>
    <string name="Setting_MoreInfo">Show More Info</string>
    <string name="Setting_Navbar_Items">Navigation Bar Items</string>
    <string name="Setting_NavidromeLogo">Show Navidrome Logo</string>
    <string name="Setting_NowPlayingLyricsBlur">Lyrics Blur Effect</string>
    <string name="Setting_ProviderDividers">Show Provider Dividers</string>
    <string name="Setting_Scrobble_Description">Navidrome Only.</string>
    <string name="Setting_Scrobble_Percent">Min. Scrobble Percentage</string>
    <string name="Setting_Transcoding">Max Bitrate</string>
    <string name="Setting_Transcoding_Data">Max Bitrate (Mobile data)</string>
    <string name="Setting_Transcoding_Wifi">Max Bitrate (WiFi)</string>
    <string name="Settings_DownloadDirectory">Download Directory</string>
    <string name="Settings_Header_Appearance">Appearance</string>
    <string name="Settings_Header_Media">Media Providers</string>
    <string name="Settings_Header_Playback">Playback</string>
    <string name="Source_Local">Local Folder</string>
    <string name="Source_Navidrome" translatable="false">Navidrome</string>
    <string name="Theme_Dark">Dark</string>
    <string name="Theme_Light">Light</string>
    <string name="Theme_System">System</string>
    <string name="app_name" translatable="false">Chora</string>
    <string name="most_played">Most Played</string>
    <string name="playlists">Playlists</string>
    <string name="radios">Internet Radio</string>
    <string name="random_songs">Explore From Your Library</string>
    <string name="recently_added">Recently Added</string>
    <string name="recently_played">Recently Played</string>
    <string name="settings">Settings</string>
    <string name="songs">Songs</string>
    <string name="welcome_text">Welcome</string>
    <style name="Theme.MusicPlayer" parent="android:Theme.Material.NoActionBar"/>
</resources>