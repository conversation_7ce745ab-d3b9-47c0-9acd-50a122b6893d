{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values/values.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "514,688,689", "startColumns": "4,4,4", "startOffsets": "32916,45509,45565", "endColumns": "45,55,54", "endOffsets": "32957,45560,45615"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\acf97b938bacc48adeb9122891289346\\transformed\\window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1"}, "to": {"startLines": "23,28,29,30,319", "startColumns": "4,4,4,4,4", "startOffsets": "1445,1624,1684,1736,20964", "endLines": "27,28,29,30,319", "endColumns": "11,59,51,44,59", "endOffsets": "1619,1679,1731,1776,21019"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\754763af6e012e597e57d1d6e7d6d983\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "376", "startColumns": "4", "startOffsets": "23931", "endColumns": "49", "endOffsets": "23976"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\ae0358a2c93f2661aabc6c05923ecb92\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "373", "startColumns": "4", "startOffsets": "23774", "endColumns": "42", "endOffsets": "23812"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "546,547,548,549,550,551,552,553,554", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "35351,35421,35483,35548,35612,35689,35754,35844,35928", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "35416,35478,35543,35607,35684,35749,35839,35923,35992"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "100,106,112,334,381,526,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,621,622,623,624,625,626,2250,2252,2253,2258,2260", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6469,6927,7330,21727,24224,33696,33929,34018,34091,34187,34282,34365,34449,34530,34615,34695,34759,34853,34932,35026,35100,35191,35263,40751,40819,40885,40961,41043,41130,147126,147302,147424,147686,147881", "endColumns": "88,70,72,61,59,73,88,72,95,94,82,83,80,84,79,63,93,78,93,73,90,71,87,67,65,75,81,86,94,65,121,60,65,66", "endOffsets": "6553,6993,7398,21784,24279,33765,34013,34086,34182,34277,34360,34444,34525,34610,34690,34754,34848,34927,35021,35095,35186,35258,35346,40814,40880,40956,41038,41125,41220,147187,147419,147480,147747,147943"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,11", "startColumns": "-1,-1,-1,-1,-1,-1,-1,4", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,406", "endColumns": "-1,-1,-1,-1,-1,-1,-1,72", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,474"}, "to": {"startLines": "409,410,513,665,669,677,679,693", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "25850,25892,32852,44232,44420,44858,44958,45767", "endColumns": "41,43,63,47,49,45,39,72", "endOffsets": "25887,25931,32911,44275,44465,44899,44993,45835"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings_screens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "455,456,627,670,673,674,692", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "28951,29010,41225,44470,44641,44699,45718", "endColumns": "58,54,51,66,57,59,48", "endOffsets": "29005,29060,41272,44532,44694,44754,45762"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\7109a23afcec01fecc6451570c536a6e\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "343,374", "startColumns": "4,4", "startOffsets": "22205,23817", "endColumns": "41,59", "endOffsets": "22242,23872"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "383,559,560,561,562,563,564,565,566,567,568,571,572,573,574,575,576,577,578,579,580,581,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,1802,1812", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24341,36252,36340,36426,36507,36591,36660,36725,36808,36914,37000,37120,37174,37243,37304,37373,37462,37557,37631,37728,37821,37919,38068,38159,38247,38343,38441,38505,38573,38660,38754,38821,38893,38965,39066,39175,39251,39320,39368,39434,39498,39572,39629,39686,39758,39808,39862,39933,40004,40074,40143,40201,40277,40348,40422,40508,40558,40628,116830,117545", "endLines": "383,559,560,561,562,563,564,565,566,567,570,571,572,573,574,575,576,577,578,579,580,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,1811,1814", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "24409,36335,36421,36502,36586,36655,36720,36803,36909,36995,37115,37169,37238,37299,37368,37457,37552,37626,37723,37816,37914,38063,38154,38242,38338,38436,38500,38568,38655,38749,38816,38888,38960,39061,39170,39246,39315,39363,39429,39493,39567,39624,39681,39753,39803,39857,39928,39999,40069,40138,40196,40272,40343,40417,40503,40553,40623,40688,117540,117693"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2ef59a3900bc6a0162f77e6c6369129b\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "375", "startColumns": "4", "startOffsets": "23877", "endColumns": "53", "endOffsets": "23926"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "21,43,44,57,58,98,99,207,208,209,210,211,212,213,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,332,333,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,391,515,516,517,518,519,520,521,683,2245,2246,2251,2254,2259,2491,2492", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1329,2553,2625,3673,3738,6337,6406,13692,13762,13830,13902,13972,14033,14107,16698,16759,16820,16882,16946,17008,17069,17137,17237,17297,17363,17436,17505,17562,17614,18562,18634,18710,18775,18834,18893,18953,19013,19073,19133,19193,19253,19313,19373,19433,19493,19552,19612,19672,19732,19792,19852,19912,19972,20032,20092,20152,20211,20271,20331,20390,20449,20508,20567,20626,21657,21692,22460,22515,22578,22633,22691,22747,22805,22866,22929,22986,23037,23095,23145,23206,23263,23329,23363,23398,24946,32962,33029,33101,33170,33239,33313,33385,45126,146808,146925,147192,147485,147752,165504,165576", "endLines": "21,43,44,57,58,98,99,207,208,209,210,211,212,213,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,332,333,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,391,515,516,517,518,519,520,521,683,2245,2249,2251,2257,2259,2491,2492", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "1384,2620,2708,3733,3799,6401,6464,13757,13825,13897,13967,14028,14102,14175,16754,16815,16877,16941,17003,17064,17132,17232,17292,17358,17431,17500,17557,17609,17671,18629,18705,18770,18829,18888,18948,19008,19068,19128,19188,19248,19308,19368,19428,19488,19547,19607,19667,19727,19787,19847,19907,19967,20027,20087,20147,20206,20266,20326,20385,20444,20503,20562,20621,20680,21687,21722,22510,22573,22628,22686,22742,22800,22861,22924,22981,23032,23090,23140,23201,23258,23324,23358,23393,23428,25011,33024,33096,33165,33234,33308,33380,33468,45192,146920,147121,147297,147681,147876,165571,165638"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,35,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,384,385,386,387,388,556,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,2261,2266,2269,2275,2278,2281,2284,2285,2286,2323,2343,2363,2366,2382,2389,2493,2497", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,366,433,493,555,628,696,753,822,884,945,1018,1086,1147,1201,1264,2012,5157,5218,5284,5350,5420,5491,5569,5648,5711,5775,5843,5912,5987,6063,6125,6188,6262,15350,15426,15499,15574,15647,15713,15766,15829,15895,15952,16027,16105,16180,16255,16315,16375,16435,16501,16566,16626,24414,24499,24587,24673,24760,36054,41277,41340,41409,41480,41555,41639,41721,41786,41839,41984,42120,42260,42402,42536,42679,42819,42903,42965,43037,43103,43169,43233,43295,43373,43451,43505,43557,43609,43677,43747,43810,43878,43952,44008,147948,148291,148463,148863,149038,149251,149465,149566,149667,152396,153873,155372,155545,156799,157320,165643,165941", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,35,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,384,385,386,387,388,556,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,2265,2268,2274,2277,2280,2283,2284,2285,2286,2342,2362,2365,2368,2388,2395,2496,2500", "endColumns": "66,66,59,61,72,67,56,68,61,60,72,67,60,53,62,64,39,60,65,65,69,70,77,78,62,63,67,68,74,75,61,62,73,74,75,72,74,72,65,52,62,65,56,74,77,74,74,59,59,59,65,64,59,71,84,87,85,86,59,85,62,68,70,74,83,81,64,52,144,135,139,141,133,142,139,83,61,71,65,65,63,61,77,77,53,51,51,67,69,62,67,73,55,63,12,12,12,12,12,12,100,100,92,12,12,12,12,12,12,12,12", "endOffsets": "361,428,488,550,623,691,748,817,879,940,1013,1081,1142,1196,1259,1324,2047,5213,5279,5345,5415,5486,5564,5643,5706,5770,5838,5907,5982,6058,6120,6183,6257,6332,15421,15494,15569,15642,15708,15761,15824,15890,15947,16022,16100,16175,16250,16310,16370,16430,16496,16561,16621,16693,24494,24582,24668,24755,24815,36135,41335,41404,41475,41550,41634,41716,41781,41834,41979,42115,42255,42397,42531,42674,42814,42898,42960,43032,43098,43164,43228,43290,43368,43446,43500,43552,43604,43672,43742,43805,43873,43947,44003,44067,148286,148458,148858,149033,149246,149460,149561,149662,149755,153868,155367,155540,155709,157315,157843,165936,166220"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\e8868848eae7ccc45186e654e202dd67\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "512", "startColumns": "4", "startOffsets": "32769", "endColumns": "82", "endOffsets": "32847"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\902f73074689cc7f7e9e84c151d54f0b\\transformed\\navigation-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "335", "startColumns": "4", "startOffsets": "21789", "endColumns": "52", "endOffsets": "21837"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings_settings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "29065,29128,29183,29247,29310,29369,29428,29510,29590,29653,29713,29783,29853,29929,30005,30078,30156,30216,30295,30367,30441,30507,30573", "endColumns": "62,54,63,62,58,58,81,79,62,59,69,69,75,75,72,77,59,78,71,73,65,65,61", "endOffsets": "29123,29178,29242,29305,29364,29423,29505,29585,29648,29708,29778,29848,29924,30000,30073,30151,30211,30290,30362,30436,30502,30568,30630"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "620", "startColumns": "4", "startOffsets": "40693", "endColumns": "57", "endOffsets": "40746"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings_dialogs.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "393,394,395,406,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,480,481,482,483,484", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25059,25104,25153,25699,25936,25989,26041,26092,26156,26225,26293,26360,26441,26502,26558,26619,26687,26734,26793,26854,26936,26997,27053,27158,27228,27289,27353,27416,27479,27542,27616,27685,27748,27822,27891,27948,28003,28070,28131,28181,28255,28326,28461,28550,28654,28732,28803,28886,30635,30689,30765,30809,30855", "endColumns": "44,48,50,48,52,51,50,63,68,67,66,80,60,55,60,67,46,58,60,81,60,55,104,69,60,63,62,62,62,73,68,62,73,68,56,54,66,60,49,73,70,134,88,103,77,70,82,64,53,75,43,45,47", "endOffsets": "25099,25148,25199,25743,25984,26036,26087,26151,26220,26288,26355,26436,26497,26553,26614,26682,26729,26788,26849,26931,26992,27048,27153,27223,27284,27348,27411,27474,27537,27611,27680,27743,27817,27886,27943,27998,28065,28126,28176,28250,28321,28456,28545,28649,28727,28798,28881,28946,30684,30760,30804,30850,30898"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\adbf5b882905ad8bd23ebd88774c480b\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "330,337", "startColumns": "4,4", "startOffsets": "21537,21885", "endColumns": "53,66", "endOffsets": "21586,21947"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "318,321,322,327,329,378,522,523,525,527,528,557,558,662,663,671,672,676,678,680,681,682,685,686,687,1799,1815,1818", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20890,21074,21132,21397,21482,24045,33473,33538,33630,33770,33871,36140,36192,44072,44134,44537,44587,44812,44904,44998,45044,45086,45336,45383,45419,116718,117698,117809", "endLines": "318,321,322,327,329,378,522,523,525,527,528,557,558,662,663,671,672,676,678,680,681,682,685,686,687,1801,1817,1822", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "20959,21127,21182,21443,21532,24093,33533,33587,33691,33866,33924,36187,36247,44129,44183,44582,44636,44853,44953,45039,45081,45121,45378,45414,45504,116825,117804,118061"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,31,32,33,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,102,103,104,105,107,108,109,110,111,113,114,115,116,117,118,119,120,121,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,214,215,219,220,221,222,223,224,225,271,272,273,274,275,276,277,278,314,315,316,317,328,341,342,347,371,379,380,382,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,675,694,695,696,697,698,699,707,708,712,716,720,725,731,738,742,746,751,755,759,763,767,771,775,781,785,791,795,801,805,810,814,817,821,827,831,837,841,847,850,854,858,862,866,870,871,872,873,876,879,882,885,889,890,891,892,893,896,898,900,902,907,908,912,918,922,923,925,937,938,942,948,952,953,954,958,985,989,990,994,1022,1194,1220,1391,1417,1448,1456,1462,1478,1500,1505,1510,1520,1529,1538,1542,1549,1568,1575,1576,1585,1588,1591,1595,1599,1603,1606,1607,1612,1617,1627,1632,1639,1645,1646,1649,1653,1658,1660,1662,1665,1668,1670,1674,1677,1684,1687,1690,1694,1696,1700,1702,1704,1706,1710,1718,1726,1738,1744,1753,1756,1767,1770,1771,1776,1777,1823,1892,1962,1963,1973,1982,2134,2136,2140,2143,2146,2149,2152,2155,2158,2161,2165,2168,2171,2174,2178,2181,2185,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2211,2213,2214,2215,2216,2217,2218,2219,2220,2222,2223,2225,2226,2228,2230,2231,2233,2234,2235,2236,2237,2238,2240,2241,2242,2243,2244,2287,2289,2291,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2304,2305,2307,2308,2309,2310,2311,2312,2313,2315,2319,2370,2371,2372,2373,2374,2375,2379,2380,2381,2396,2398,2400,2402,2404,2406,2407,2408,2409,2411,2413,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2429,2430,2431,2432,2434,2436,2437,2439,2440,2442,2444,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2457,2459,2460,2461,2462,2464,2465,2466,2467,2468,2470,2472,2474,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,1781,1822,1877,2052,2116,2186,2247,2322,2398,2475,2713,2798,2880,2956,3032,3109,3187,3293,3399,3478,3558,3615,3804,3878,3953,4018,4084,4144,4205,4277,4350,4417,4485,4544,4603,4662,4721,4780,4834,4888,4941,4995,5049,5103,6627,6701,6780,6853,6998,7070,7142,7215,7272,7403,7477,7551,7626,7698,7771,7841,7912,7972,8243,8312,8381,8451,8525,8601,8665,8742,8818,8895,8960,9029,9106,9181,9250,9318,9395,9461,9522,9619,9684,9753,9852,9923,9982,10040,10097,10156,10220,10291,10363,10435,10507,10579,10646,10714,10782,10841,10904,10968,11058,11149,11209,11275,11342,11408,11478,11542,11595,11662,11723,11790,11903,11961,12024,12089,12154,12229,12302,12374,12418,12465,12511,12560,12621,12682,12743,12805,12869,12933,12997,13062,13125,13185,13246,13312,13371,13431,13493,13564,13624,14180,14266,14516,14606,14693,14781,14863,14946,15036,18109,18161,18219,18264,18330,18394,18451,18508,20685,20742,20790,20839,21448,22109,22156,22414,23699,24098,24162,24284,30903,30977,31047,31125,31179,31249,31334,31382,31428,31489,31552,31618,31682,31753,31816,31881,31945,32006,32067,32119,32192,32266,32335,32410,32484,32558,32699,44759,45840,45918,46008,46096,46192,46282,46864,46953,47200,47481,47733,48018,48411,48888,49110,49332,49608,49835,50065,50295,50525,50755,50982,51401,51627,52052,52282,52710,52929,53212,53420,53551,53778,54204,54429,54856,55077,55502,55622,55898,56199,56523,56814,57128,57265,57396,57501,57743,57910,58114,58322,58593,58705,58817,58922,59039,59253,59399,59539,59625,59973,60061,60307,60725,60974,61056,61154,61811,61911,62163,62587,62842,62936,63025,63262,65286,65528,65630,65883,68039,78720,80236,90931,92459,94216,94842,95262,96523,97788,98044,98280,98827,99321,99926,100124,100704,102072,102447,102565,103103,103260,103456,103729,103985,104155,104296,104360,104725,105092,105768,106032,106370,106723,106817,107003,107309,107571,107696,107823,108062,108273,108392,108585,108762,109217,109398,109520,109779,109892,110079,110181,110288,110417,110692,111200,111696,112573,112867,113437,113586,114318,114490,114574,114910,115002,118066,123297,128668,128730,129308,129892,137839,137952,138181,138341,138493,138664,138830,138999,139166,139329,139572,139742,139915,140086,140360,140559,140764,141094,141178,141274,141370,141468,141568,141670,141772,141874,141976,142078,142178,142274,142386,142515,142638,142769,142900,142998,143112,143206,143346,143480,143576,143688,143788,143904,144000,144112,144212,144352,144488,144652,144782,144940,145090,145231,145375,145510,145622,145772,145900,146028,146164,146296,146426,146556,146668,149760,149906,150050,150188,150254,150344,150420,150524,150614,150716,150824,150932,151032,151112,151204,151302,151412,151464,151542,151648,151740,151844,151954,152076,152239,155796,155876,155976,156066,156176,156266,156507,156601,156707,157848,157948,158060,158174,158290,158406,158500,158614,158726,158828,158948,159070,159152,159256,159376,159502,159600,159694,159782,159894,160010,160132,160244,160419,160535,160621,160713,160825,160949,161016,161142,161210,161338,161482,161610,161679,161774,161889,162002,162101,162210,162321,162432,162533,162638,162738,162868,162959,163082,163176,163288,163374,163478,163574,163662,163780,163884,163988,164114,164202,164310,164410,164500,164610,164694,164796,164880,164934,164998,165104,165190,165300,165384", "endLines": "2,3,4,31,32,33,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,102,103,104,105,107,108,109,110,111,113,114,115,116,117,118,119,120,121,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,214,215,219,220,221,222,223,224,225,271,272,273,274,275,276,277,278,314,315,316,317,328,341,342,347,371,379,380,382,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,675,694,695,696,697,698,706,707,711,715,719,724,730,737,741,745,750,754,758,762,766,770,774,780,784,790,794,800,804,809,813,816,820,826,830,836,840,846,849,853,857,861,865,869,870,871,872,875,878,881,884,888,889,890,891,892,895,897,899,901,906,907,911,917,921,922,924,936,937,941,947,951,952,953,957,984,988,989,993,1021,1193,1219,1390,1416,1447,1455,1461,1477,1499,1504,1509,1519,1528,1537,1541,1548,1567,1574,1575,1584,1587,1590,1594,1598,1602,1605,1606,1611,1616,1626,1631,1638,1644,1645,1648,1652,1657,1659,1661,1664,1667,1669,1673,1676,1683,1686,1689,1693,1695,1699,1701,1703,1705,1709,1717,1725,1737,1743,1752,1755,1766,1769,1770,1775,1776,1781,1891,1961,1962,1972,1981,1982,2135,2139,2142,2145,2148,2151,2154,2157,2160,2164,2167,2170,2173,2177,2180,2184,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2210,2212,2213,2214,2215,2216,2217,2218,2219,2221,2222,2224,2225,2227,2229,2230,2232,2233,2234,2235,2236,2237,2239,2240,2241,2242,2243,2244,2288,2290,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2304,2306,2307,2308,2309,2310,2311,2312,2314,2318,2322,2370,2371,2372,2373,2374,2378,2379,2380,2381,2397,2399,2401,2403,2405,2406,2407,2408,2410,2412,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2428,2429,2430,2431,2433,2435,2436,2438,2439,2441,2443,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2458,2459,2460,2461,2463,2464,2465,2466,2467,2469,2471,2473,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "200,245,294,1817,1872,1934,2111,2181,2242,2317,2393,2470,2548,2793,2875,2951,3027,3104,3182,3288,3394,3473,3553,3610,3668,3873,3948,4013,4079,4139,4200,4272,4345,4412,4480,4539,4598,4657,4716,4775,4829,4883,4936,4990,5044,5098,5152,6696,6775,6848,6922,7065,7137,7210,7267,7325,7472,7546,7621,7693,7766,7836,7907,7967,8028,8307,8376,8446,8520,8596,8660,8737,8813,8890,8955,9024,9101,9176,9245,9313,9390,9456,9517,9614,9679,9748,9847,9918,9977,10035,10092,10151,10215,10286,10358,10430,10502,10574,10641,10709,10777,10836,10899,10963,11053,11144,11204,11270,11337,11403,11473,11537,11590,11657,11718,11785,11898,11956,12019,12084,12149,12224,12297,12369,12413,12460,12506,12555,12616,12677,12738,12800,12864,12928,12992,13057,13120,13180,13241,13307,13366,13426,13488,13559,13619,13687,14261,14348,14601,14688,14776,14858,14941,15031,15122,18156,18214,18259,18325,18389,18446,18503,18557,20737,20785,20834,20885,21477,22151,22200,22455,23726,24157,24219,24336,30972,31042,31120,31174,31244,31329,31377,31423,31484,31547,31613,31677,31748,31811,31876,31940,32001,32062,32114,32187,32261,32330,32405,32479,32553,32694,32764,44807,45913,46003,46091,46187,46277,46859,46948,47195,47476,47728,48013,48406,48883,49105,49327,49603,49830,50060,50290,50520,50750,50977,51396,51622,52047,52277,52705,52924,53207,53415,53546,53773,54199,54424,54851,55072,55497,55617,55893,56194,56518,56809,57123,57260,57391,57496,57738,57905,58109,58317,58588,58700,58812,58917,59034,59248,59394,59534,59620,59968,60056,60302,60720,60969,61051,61149,61806,61906,62158,62582,62837,62931,63020,63257,65281,65523,65625,65878,68034,78715,80231,90926,92454,94211,94837,95257,96518,97783,98039,98275,98822,99316,99921,100119,100699,102067,102442,102560,103098,103255,103451,103724,103980,104150,104291,104355,104720,105087,105763,106027,106365,106718,106812,106998,107304,107566,107691,107818,108057,108268,108387,108580,108757,109212,109393,109515,109774,109887,110074,110176,110283,110412,110687,111195,111691,112568,112862,113432,113581,114313,114485,114569,114905,114997,115275,123292,128663,128725,129303,129887,129978,137947,138176,138336,138488,138659,138825,138994,139161,139324,139567,139737,139910,140081,140355,140554,140759,141089,141173,141269,141365,141463,141563,141665,141767,141869,141971,142073,142173,142269,142381,142510,142633,142764,142895,142993,143107,143201,143341,143475,143571,143683,143783,143899,143995,144107,144207,144347,144483,144647,144777,144935,145085,145226,145370,145505,145617,145767,145895,146023,146159,146291,146421,146551,146663,146803,149901,150045,150183,150249,150339,150415,150519,150609,150711,150819,150927,151027,151107,151199,151297,151407,151459,151537,151643,151735,151839,151949,152071,152234,152391,155871,155971,156061,156171,156261,156502,156596,156702,156794,157943,158055,158169,158285,158401,158495,158609,158721,158823,158943,159065,159147,159251,159371,159497,159595,159689,159777,159889,160005,160127,160239,160414,160530,160616,160708,160820,160944,161011,161137,161205,161333,161477,161605,161674,161769,161884,161997,162096,162205,162316,162427,162528,162633,162733,162863,162954,163077,163171,163283,163369,163473,163569,163657,163775,163879,163983,164109,164197,164305,164405,164495,164605,164689,164791,164875,164929,164993,165099,165185,165295,165379,165499"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings_actions.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "392,396,397,398,399,400,401,402,403,404,405,407,408", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25016,25204,25270,25315,25368,25413,25462,25509,25554,25603,25650,25748,25799", "endColumns": "42,65,44,52,44,48,46,44,48,46,48,50,50", "endOffsets": "25054,25265,25310,25363,25408,25457,25504,25549,25598,25645,25694,25794,25845"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4ec837fe980ac62f82a7402163f3b9e1\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "326", "startColumns": "4", "startOffsets": "21331", "endColumns": "65", "endOffsets": "21392"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "34,101,264,265,266,267,268,269,270,338,339,340,389,390,524,555,664,666,684,690,691,1782,1983,1986,1992,1998,2001,2007,2011,2014,2021,2027,2030,2036,2041,2046,2053,2055,2061,2067,2075,2080,2087,2092,2098,2102,2109,2113,2119,2125,2128,2132,2133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1939,6558,17676,17740,17795,17863,17930,17995,18052,21952,22000,22048,24820,24883,33592,35997,44188,44280,45197,45620,45670,115280,129983,130088,130333,130671,130817,131157,131369,131532,131939,132277,132400,132739,132978,133235,133606,133666,134004,134290,134739,135031,135419,135724,136068,136313,136643,136850,137118,137391,137535,137736,137783", "endLines": "34,101,264,265,266,267,268,269,270,338,339,340,389,390,524,555,664,668,684,690,691,1798,1985,1991,1997,2000,2006,2010,2013,2020,2026,2029,2035,2040,2045,2052,2054,2060,2066,2074,2079,2086,2091,2097,2101,2108,2112,2118,2124,2127,2131,2132,2133", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55", "endOffsets": "2007,6622,17735,17790,17858,17925,17990,18047,18104,21995,22043,22104,24878,24941,33625,36049,44227,44415,45331,45665,45713,116713,130083,130328,130666,130812,131152,131364,131527,131934,132272,132395,132734,132973,133230,133601,133661,133999,134285,134734,135026,135414,135719,136063,136308,136638,136845,137113,137386,137530,137731,137778,137834"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\cc1d3805fe8d4fc761fa3c8cb2574116\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "324,325,336,344,345,366,367,368,369,370", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "21244,21284,21842,22247,22302,23433,23487,23539,23588,23649", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "21279,21326,21880,22297,22344,23482,23534,23583,23644,23694"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\6a19f80353fbc6fa60e88fd607d06113\\transformed\\recyclerview-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "22,216,217,218,226,227,228,331", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1389,14353,14412,14460,15127,15202,15278,21591", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "1440,14407,14455,14511,15197,15273,15345,21652"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4ff8829df674d3dbd062b8350784bf54\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "323,346,377", "startColumns": "4,4,4", "startOffsets": "21187,22349,23981", "endColumns": "56,64,63", "endOffsets": "21239,22409,24040"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2369", "startColumns": "4", "startOffsets": "155714", "endColumns": "81", "endOffsets": "155791"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\6cddd03f981b66f6053ec06c3046d7ce\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "372", "startColumns": "4", "startOffsets": "23731", "endColumns": "42", "endOffsets": "23769"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,7,5,6", "startColumns": "-1,4,4,4", "startOffsets": "-1,246,130,187", "endColumns": "-1,53,55,57", "endOffsets": "-1,295,181,240"}, "to": {"startLines": "122,123,124,125", "startColumns": "4,4,4,4", "startOffsets": "8033,8075,8129,8185", "endColumns": "41,53,55,57", "endOffsets": "8070,8124,8180,8238"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c7890e83d7f221a44550e26073b9aa79\\transformed\\coil-base-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "320", "startColumns": "4", "startOffsets": "21024", "endColumns": "49", "endOffsets": "21069"}}]}, {"outputFile": "E:\\android_development_cache\\.gradle\\daemon\\8.11.1\\com.craftworks.music.app-mergeDebugResources-3:\\values\\values.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,101,157", "endColumns": "45,55,54", "endOffsets": "96,152,207"}, "to": {"startLines": "511,685,686", "startColumns": "4,4,4", "startOffsets": "32748,45341,45397", "endColumns": "45,55,54", "endOffsets": "32789,45392,45447"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\acf97b938bacc48adeb9122891289346\\transformed\\window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,7,8,9,10,11,19,23,34,51", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,234,294,346,391,451,869,1066,1790,2904", "endLines": "6,7,8,9,10,18,22,33,50,58", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "229,289,341,386,446,864,1061,1785,2899,3292"}, "to": {"startLines": "23,28,29,30,316,2601,2614,3899,3907,3919", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "1445,1624,1684,1736,20796,169507,170006,213524,213806,214246", "endLines": "27,28,29,30,316,2606,2617,3906,3918,3926", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "1619,1679,1731,1776,20851,169697,170132,213801,214241,214550"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\754763af6e012e597e57d1d6e7d6d983\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "373", "startColumns": "4", "startOffsets": "23763", "endColumns": "49", "endOffsets": "23808"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\ae0358a2c93f2661aabc6c05923ecb92\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "370", "startColumns": "4", "startOffsets": "23606", "endColumns": "42", "endOffsets": "23644"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "543,544,545,546,547,548,549,550,551", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "35183,35253,35315,35380,35444,35521,35586,35676,35760", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "35248,35310,35375,35439,35516,35581,35671,35755,35824"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,387,449,509,583,672,745,841,936,1019,1103,1184,1269,1349,1413,1507,1586,1680,1754,1845,1917,2005,2073,2139,2215,2297,2384,2479,2545,2667,2728,2794", "endColumns": "88,70,72,61,59,73,88,72,95,94,82,83,80,84,79,63,93,78,93,73,90,71,87,67,65,75,81,86,94,65,121,60,65,66", "endOffsets": "139,210,283,444,504,578,667,740,836,931,1014,1098,1179,1264,1344,1408,1502,1581,1675,1749,1840,1912,2000,2068,2134,2210,2292,2379,2474,2540,2662,2723,2789,2856"}, "to": {"startLines": "100,106,112,331,378,523,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,618,619,620,621,622,623,2246,2248,2249,2254,2256", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6469,6927,7330,21559,24056,33528,33761,33850,33923,34019,34114,34197,34281,34362,34447,34527,34591,34685,34764,34858,34932,35023,35095,40583,40651,40717,40793,40875,40962,146885,147061,147183,147445,147640", "endColumns": "88,70,72,61,59,73,88,72,95,94,82,83,80,84,79,63,93,78,93,73,90,71,87,67,65,75,81,86,94,65,121,60,65,66", "endOffsets": "6553,6993,7398,21616,24111,33597,33845,33918,34014,34109,34192,34276,34357,34442,34522,34586,34680,34759,34853,34927,35018,35090,35178,40646,40712,40788,40870,40957,41052,146946,147178,147239,147506,147702"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "4,5,1,8,7,6,3", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "148,191,17,334,283,236,107", "endColumns": "41,43,63,47,49,45,39", "endOffsets": "185,230,76,377,328,277,142"}, "to": {"startLines": "406,407,510,662,666,674,676", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "25682,25724,32684,44064,44252,44690,44790", "endColumns": "41,43,63,47,49,45,39", "endOffsets": "25719,25763,32743,44107,44297,44731,44825"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings_screens.xml", "from": {"startLines": "10,11,5,7,4,6,3", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "398,458,185,299,126,238,76", "endColumns": "58,54,51,66,57,59,48", "endOffsets": "452,508,232,361,179,293,120"}, "to": {"startLines": "452,453,624,667,670,671,689", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "28783,28842,41057,44302,44473,44531,45550", "endColumns": "58,54,51,66,57,59,48", "endOffsets": "28837,28892,41104,44364,44526,44586,45594"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\7109a23afcec01fecc6451570c536a6e\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "340,371", "startColumns": "4,4", "startOffsets": "22037,23649", "endColumns": "41,59", "endOffsets": "22074,23704"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "380,556,557,558,559,560,561,562,563,564,565,568,569,570,571,572,573,574,575,576,577,578,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,1798,1808", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24173,36084,36172,36258,36339,36423,36492,36557,36640,36746,36832,36952,37006,37075,37136,37205,37294,37389,37463,37560,37653,37751,37900,37991,38079,38175,38273,38337,38405,38492,38586,38653,38725,38797,38898,39007,39083,39152,39200,39266,39330,39404,39461,39518,39590,39640,39694,39765,39836,39906,39975,40033,40109,40180,40254,40340,40390,40460,116589,117304", "endLines": "380,556,557,558,559,560,561,562,563,564,567,568,569,570,571,572,573,574,575,576,577,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,1807,1810", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "24241,36167,36253,36334,36418,36487,36552,36635,36741,36827,36947,37001,37070,37131,37200,37289,37384,37458,37555,37648,37746,37895,37986,38074,38170,38268,38332,38400,38487,38581,38648,38720,38792,38893,39002,39078,39147,39195,39261,39325,39399,39456,39513,39585,39635,39689,39760,39831,39901,39970,40028,40104,40175,40249,40335,40385,40455,40520,117299,117452"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2ef59a3900bc6a0162f77e6c6369129b\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "372", "startColumns": "4", "startOffsets": "23709", "endColumns": "53", "endOffsets": "23758"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "21,43,44,57,58,98,99,204,205,206,207,208,209,210,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,329,330,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,388,512,513,514,515,516,517,518,680,2241,2242,2247,2250,2255,2487,2488,3164,3209,3289,3324,3354,3387", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1329,2553,2625,3673,3738,6337,6406,13524,13594,13662,13734,13804,13865,13939,16530,16591,16652,16714,16778,16840,16901,16969,17069,17129,17195,17268,17337,17394,17446,18394,18466,18542,18607,18666,18725,18785,18845,18905,18965,19025,19085,19145,19205,19265,19325,19384,19444,19504,19564,19624,19684,19744,19804,19864,19924,19984,20043,20103,20163,20222,20281,20340,20399,20458,21489,21524,22292,22347,22410,22465,22523,22579,22637,22698,22761,22818,22869,22927,22977,23038,23095,23161,23195,23230,24778,32794,32861,32933,33002,33071,33145,33217,44958,146567,146684,146951,147244,147511,165263,165335,187928,189902,193010,194816,195816,196498", "endLines": "21,43,44,57,58,98,99,204,205,206,207,208,209,210,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,329,330,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,388,512,513,514,515,516,517,518,680,2241,2245,2247,2253,2255,2487,2488,3169,3218,3323,3344,3386,3392", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1384,2620,2708,3733,3799,6401,6464,13589,13657,13729,13799,13860,13934,14007,16586,16647,16709,16773,16835,16896,16964,17064,17124,17190,17263,17332,17389,17441,17503,18461,18537,18602,18661,18720,18780,18840,18900,18960,19020,19080,19140,19200,19260,19320,19379,19439,19499,19559,19619,19679,19739,19799,19859,19919,19979,20038,20098,20158,20217,20276,20335,20394,20453,20512,21519,21554,22342,22405,22460,22518,22574,22632,22693,22756,22813,22864,22922,22972,23033,23090,23156,23190,23225,23260,24843,32856,32928,32997,33066,33140,33212,33300,45024,146679,146880,147056,147440,147635,165330,165397,188126,190198,194811,195492,196493,196660"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,101,104,110,113,116,119,120,121,122,142,162,165,168,175,182,186,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,189,249,311,384,452,509,578,640,701,774,842,903,957,1020,1085,1125,1186,1252,1318,1388,1459,1537,1616,1679,1743,1811,1880,1955,2031,2093,2156,2230,2305,2381,2454,2529,2602,2668,2721,2784,2850,2907,2982,3060,3135,3210,3270,3330,3390,3456,3521,3581,3653,3738,3826,3912,3999,4059,4145,4208,4277,4348,4423,4507,4589,4654,4707,4852,4988,5128,5270,5404,5547,5687,5771,5833,5905,5971,6037,6101,6163,6241,6319,6373,6425,6477,6545,6615,6678,6746,6820,6876,6940,7283,7455,7855,8030,8243,8457,8558,8659,8752,10229,11728,11901,12070,12591,13119,13417,13701", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,100,103,109,112,115,118,119,120,121,141,161,164,167,174,181,185,189,200", "endColumns": "66,66,59,61,72,67,56,68,61,60,72,67,60,53,62,64,39,60,65,65,69,70,77,78,62,63,67,68,74,75,61,62,73,74,75,72,74,72,65,52,62,65,56,74,77,74,74,59,59,59,65,64,59,71,84,87,85,86,59,85,62,68,70,74,83,81,64,52,144,135,139,141,133,142,139,83,61,71,65,65,63,61,77,77,53,51,51,67,69,62,67,73,55,63,12,12,12,12,12,12,100,100,92,12,12,12,12,12,12,12,12,24", "endOffsets": "117,184,244,306,379,447,504,573,635,696,769,837,898,952,1015,1080,1120,1181,1247,1313,1383,1454,1532,1611,1674,1738,1806,1875,1950,2026,2088,2151,2225,2300,2376,2449,2524,2597,2663,2716,2779,2845,2902,2977,3055,3130,3205,3265,3325,3385,3451,3516,3576,3648,3733,3821,3907,3994,4054,4140,4203,4272,4343,4418,4502,4584,4649,4702,4847,4983,5123,5265,5399,5542,5682,5766,5828,5900,5966,6032,6096,6158,6236,6314,6368,6420,6472,6540,6610,6673,6741,6815,6871,6935,7278,7450,7850,8025,8238,8452,8553,8654,8747,10224,11723,11896,12065,12586,13114,13412,13696,14085"}, "to": {"startLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,35,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,381,382,383,384,385,553,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,2257,2262,2265,2271,2274,2277,2280,2281,2282,2319,2339,2359,2362,2378,2385,2489,2493,3439", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,366,433,493,555,628,696,753,822,884,945,1018,1086,1147,1201,1264,2012,5157,5218,5284,5350,5420,5491,5569,5648,5711,5775,5843,5912,5987,6063,6125,6188,6262,15182,15258,15331,15406,15479,15545,15598,15661,15727,15784,15859,15937,16012,16087,16147,16207,16267,16333,16398,16458,24246,24331,24419,24505,24592,35886,41109,41172,41241,41312,41387,41471,41553,41618,41671,41816,41952,42092,42234,42368,42511,42651,42735,42797,42869,42935,43001,43065,43127,43205,43283,43337,43389,43441,43509,43579,43642,43710,43784,43840,147707,148050,148222,148622,148797,149010,149224,149325,149426,152155,153632,155131,155304,156558,157079,165402,165700,198210", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,35,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,381,382,383,384,385,553,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,2261,2264,2270,2273,2276,2279,2280,2281,2282,2338,2358,2361,2364,2384,2391,2492,2496,3449", "endColumns": "66,66,59,61,72,67,56,68,61,60,72,67,60,53,62,64,39,60,65,65,69,70,77,78,62,63,67,68,74,75,61,62,73,74,75,72,74,72,65,52,62,65,56,74,77,74,74,59,59,59,65,64,59,71,84,87,85,86,59,85,62,68,70,74,83,81,64,52,144,135,139,141,133,142,139,83,61,71,65,65,63,61,77,77,53,51,51,67,69,62,67,73,55,63,12,12,12,12,12,12,100,100,92,12,12,12,12,12,12,12,12,24", "endOffsets": "361,428,488,550,623,691,748,817,879,940,1013,1081,1142,1196,1259,1324,2047,5213,5279,5345,5415,5486,5564,5643,5706,5770,5838,5907,5982,6058,6120,6183,6257,6332,15253,15326,15401,15474,15540,15593,15656,15722,15779,15854,15932,16007,16082,16142,16202,16262,16328,16393,16453,16525,24326,24414,24500,24587,24647,35967,41167,41236,41307,41382,41466,41548,41613,41666,41811,41947,42087,42229,42363,42506,42646,42730,42792,42864,42930,42996,43060,43122,43200,43278,43332,43384,43436,43504,43574,43637,43705,43779,43835,43899,148045,148217,148617,148792,149005,149219,149320,149421,149514,153627,155126,155299,155468,157074,157602,165695,165979,198594"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\e8868848eae7ccc45186e654e202dd67\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "509", "startColumns": "4", "startOffsets": "32601", "endColumns": "82", "endOffsets": "32679"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\902f73074689cc7f7e9e84c151d54f0b\\transformed\\navigation-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "332,2607,3651,3654", "startColumns": "4,4,4,4", "startOffsets": "21621,169702,205373,205488", "endLines": "332,2613,3653,3656", "endColumns": "52,24,24,24", "endOffsets": "21669,170001,205483,205598"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings_settings.xml", "from": {"startLines": "19,21,3,18,24,23,20,9,22,5,4,7,6,8,16,15,12,14,13,17,2,10,11", "startColumns": "8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,4,4,4", "startOffsets": "1323,1478,128,1255,1670,1606,1391,574,1538,272,197,418,337,493,1098,1015,789,931,854,1176,57,655,722", "endColumns": "66,58,67,66,62,62,85,83,66,63,73,73,79,79,76,81,63,82,75,77,65,65,61", "endOffsets": "1381,1528,187,1313,1724,1660,1468,649,1596,327,262,483,408,564,1166,1088,844,1005,921,1245,118,716,779"}, "to": {"startLines": "454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28897,28960,29015,29079,29142,29201,29260,29342,29422,29485,29545,29615,29685,29761,29837,29910,29988,30048,30127,30199,30273,30339,30405", "endColumns": "62,54,63,62,58,58,81,79,62,59,69,69,75,75,72,77,59,78,71,73,65,65,61", "endOffsets": "28955,29010,29074,29137,29196,29255,29337,29417,29480,29540,29610,29680,29756,29832,29905,29983,30043,30122,30194,30268,30334,30400,30462"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "617", "startColumns": "4", "startOffsets": "40525", "endColumns": "57", "endOffsets": "40578"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings_dialogs.xml", "from": {"startLines": "79,76,77,78,6,5,4,32,43,3,41,68,15,33,40,67,9,28,71,25,74,75,42,69,20,47,24,22,23,50,73,44,37,36,34,35,72,70,53,56,57,16,61,64,60,62,63,29,18,17,10,11,12", "startColumns": "4,4,4,4,8,8,8,4,4,4,4,4,4,4,4,4,4,4,4,12,4,4,8,4,12,4,12,12,12,4,4,8,8,8,8,8,4,4,4,4,4,8,4,4,4,4,4,8,8,8,8,8,8", "startOffsets": "4063,3911,3961,4013,287,230,174,1559,2222,101,2044,3377,607,1624,1982,3308,374,1406,3592,1295,3792,3854,2116,3459,981,2383,1223,1079,1151,2472,3722,2296,1881,1807,1685,1747,3654,3530,2571,2656,2731,673,2910,3156,2831,3000,3072,1470,894,813,426,475,526", "endColumns": "44,48,50,48,56,55,54,63,68,67,66,80,60,55,60,67,46,58,60,89,60,55,108,69,68,63,70,70,70,73,68,66,77,72,60,58,66,60,49,73,70,138,88,103,77,70,82,68,57,79,47,49,51", "endOffsets": "4103,3955,4007,4057,335,277,220,1618,2286,164,2106,3453,663,1675,2038,3371,416,1460,3648,1372,3848,3905,2216,3524,1037,2442,1281,1137,1209,2541,3786,2354,1950,1871,1737,1797,3716,3586,2616,2725,2797,803,2994,3255,2904,3066,3150,1530,943,884,465,516,569"}, "to": {"startLines": "390,391,392,403,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,477,478,479,480,481", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24891,24936,24985,25531,25768,25821,25873,25924,25988,26057,26125,26192,26273,26334,26390,26451,26519,26566,26625,26686,26768,26829,26885,26990,27060,27121,27185,27248,27311,27374,27448,27517,27580,27654,27723,27780,27835,27902,27963,28013,28087,28158,28293,28382,28486,28564,28635,28718,30467,30521,30597,30641,30687", "endColumns": "44,48,50,48,52,51,50,63,68,67,66,80,60,55,60,67,46,58,60,81,60,55,104,69,60,63,62,62,62,73,68,62,73,68,56,54,66,60,49,73,70,134,88,103,77,70,82,64,53,75,43,45,47", "endOffsets": "24931,24980,25031,25575,25816,25868,25919,25983,26052,26120,26187,26268,26329,26385,26446,26514,26561,26620,26681,26763,26824,26880,26985,27055,27116,27180,27243,27306,27369,27443,27512,27575,27649,27718,27775,27830,27897,27958,28008,28082,28153,28288,28377,28481,28559,28630,28713,28778,30516,30592,30636,30682,30730"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\adbf5b882905ad8bd23ebd88774c480b\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "327,334", "startColumns": "4,4", "startOffsets": "21369,21717", "endColumns": "53,66", "endOffsets": "21418,21779"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641,3752", "endLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,65,70", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3747,4004"}, "to": {"startLines": "315,318,319,324,326,375,519,520,522,524,525,554,555,659,660,668,669,673,675,677,678,679,682,683,684,1795,1811,1814", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20722,20906,20964,21229,21314,23877,33305,33370,33462,33602,33703,35972,36024,43904,43966,44369,44419,44644,44736,44830,44876,44918,45168,45215,45251,116477,117457,117568", "endLines": "315,318,319,324,326,375,519,520,522,524,525,554,555,659,660,668,669,673,675,677,678,679,682,683,684,1797,1813,1818", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "20791,20959,21014,21275,21364,23925,33365,33419,33523,33698,33756,36019,36079,43961,44015,44414,44468,44685,44785,44871,44913,44953,45210,45246,45336,116584,117563,117820"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3597,3669,3741,3814,3871,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3664,3736,3809,3866,3924,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,31,32,33,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,102,103,104,105,107,108,109,110,111,113,114,115,116,117,118,119,120,121,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,211,212,216,217,218,219,220,221,222,268,269,270,271,272,273,274,275,311,312,313,314,325,338,339,344,368,376,377,379,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,672,690,691,692,693,694,695,703,704,708,712,716,721,727,734,738,742,747,751,755,759,763,767,771,777,781,787,791,797,801,806,810,813,817,823,827,833,837,843,846,850,854,858,862,866,867,868,869,872,875,878,881,885,886,887,888,889,892,894,896,898,903,904,908,914,918,919,921,933,934,938,944,948,949,950,954,981,985,986,990,1018,1190,1216,1387,1413,1444,1452,1458,1474,1496,1501,1506,1516,1525,1534,1538,1545,1564,1571,1572,1581,1584,1587,1591,1595,1599,1602,1603,1608,1613,1623,1628,1635,1641,1642,1645,1649,1654,1656,1658,1661,1664,1666,1670,1673,1680,1683,1686,1690,1692,1696,1698,1700,1702,1706,1714,1722,1734,1740,1749,1752,1763,1766,1767,1772,1773,1819,1888,1958,1959,1969,1978,2130,2132,2136,2139,2142,2145,2148,2151,2154,2157,2161,2164,2167,2170,2174,2177,2181,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2207,2209,2210,2211,2212,2213,2214,2215,2216,2218,2219,2221,2222,2224,2226,2227,2229,2230,2231,2232,2233,2234,2236,2237,2238,2239,2240,2283,2285,2287,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2303,2304,2305,2306,2307,2308,2309,2311,2315,2366,2367,2368,2369,2370,2371,2375,2376,2377,2392,2394,2396,2398,2400,2402,2403,2404,2405,2407,2409,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2425,2426,2427,2428,2430,2432,2433,2435,2436,2438,2440,2442,2443,2444,2445,2446,2447,2448,2449,2450,2451,2452,2453,2455,2456,2457,2458,2460,2461,2462,2463,2464,2466,2468,2470,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2497,2572,2575,2578,2581,2595,2618,2660,2663,2692,2719,2728,2792,3160,3181,3219,3267,3393,3417,3423,3450,3471,3595,3662,3668,3812,3839,3887,3947,4047,4067,4122,4134,4160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,1781,1822,1877,2052,2116,2186,2247,2322,2398,2475,2713,2798,2880,2956,3032,3109,3187,3293,3399,3478,3558,3615,3804,3878,3953,4018,4084,4144,4205,4277,4350,4417,4485,4544,4603,4662,4721,4780,4834,4888,4941,4995,5049,5103,6627,6701,6780,6853,6998,7070,7142,7215,7272,7403,7477,7551,7626,7698,7771,7841,7912,7972,8075,8144,8213,8283,8357,8433,8497,8574,8650,8727,8792,8861,8938,9013,9082,9150,9227,9293,9354,9451,9516,9585,9684,9755,9814,9872,9929,9988,10052,10123,10195,10267,10339,10411,10478,10546,10614,10673,10736,10800,10890,10981,11041,11107,11174,11240,11310,11374,11427,11494,11555,11622,11735,11793,11856,11921,11986,12061,12134,12206,12250,12297,12343,12392,12453,12514,12575,12637,12701,12765,12829,12894,12957,13017,13078,13144,13203,13263,13325,13396,13456,14012,14098,14348,14438,14525,14613,14695,14778,14868,17941,17993,18051,18096,18162,18226,18283,18340,20517,20574,20622,20671,21280,21941,21988,22246,23531,23930,23994,24116,30735,30809,30879,30957,31011,31081,31166,31214,31260,31321,31384,31450,31514,31585,31648,31713,31777,31838,31899,31951,32024,32098,32167,32242,32316,32390,32531,44591,45599,45677,45767,45855,45951,46041,46623,46712,46959,47240,47492,47777,48170,48647,48869,49091,49367,49594,49824,50054,50284,50514,50741,51160,51386,51811,52041,52469,52688,52971,53179,53310,53537,53963,54188,54615,54836,55261,55381,55657,55958,56282,56573,56887,57024,57155,57260,57502,57669,57873,58081,58352,58464,58576,58681,58798,59012,59158,59298,59384,59732,59820,60066,60484,60733,60815,60913,61570,61670,61922,62346,62601,62695,62784,63021,65045,65287,65389,65642,67798,78479,79995,90690,92218,93975,94601,95021,96282,97547,97803,98039,98586,99080,99685,99883,100463,101831,102206,102324,102862,103019,103215,103488,103744,103914,104055,104119,104484,104851,105527,105791,106129,106482,106576,106762,107068,107330,107455,107582,107821,108032,108151,108344,108521,108976,109157,109279,109538,109651,109838,109940,110047,110176,110451,110959,111455,112332,112626,113196,113345,114077,114249,114333,114669,114761,117825,123056,128427,128489,129067,129651,137598,137711,137940,138100,138252,138423,138589,138758,138925,139088,139331,139501,139674,139845,140119,140318,140523,140853,140937,141033,141129,141227,141327,141429,141531,141633,141735,141837,141937,142033,142145,142274,142397,142528,142659,142757,142871,142965,143105,143239,143335,143447,143547,143663,143759,143871,143971,144111,144247,144411,144541,144699,144849,144990,145134,145269,145381,145531,145659,145787,145923,146055,146185,146315,146427,149519,149665,149809,149947,150013,150103,150179,150283,150373,150475,150583,150691,150791,150871,150963,151061,151171,151223,151301,151407,151499,151603,151713,151835,151998,155555,155635,155735,155825,155935,156025,156266,156360,156466,157607,157707,157819,157933,158049,158165,158259,158373,158485,158587,158707,158829,158911,159015,159135,159261,159359,159453,159541,159653,159769,159891,160003,160178,160294,160380,160472,160584,160708,160775,160901,160969,161097,161241,161369,161438,161533,161648,161761,161860,161969,162080,162191,162292,162397,162497,162627,162718,162841,162935,163047,163133,163237,163333,163421,163539,163643,163747,163873,163961,164069,164169,164259,164369,164453,164555,164639,164693,164757,164863,164949,165059,165143,165984,168600,168718,168833,168913,169274,170137,171541,171619,172963,174324,174712,177555,187793,188532,190203,192289,196665,197416,197678,198599,198978,203256,205785,206014,210622,211677,213212,215093,219217,219961,222092,222432,223743", "endLines": "2,3,4,31,32,33,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,102,103,104,105,107,108,109,110,111,113,114,115,116,117,118,119,120,121,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,211,212,216,217,218,219,220,221,222,268,269,270,271,272,273,274,275,311,312,313,314,325,338,339,344,368,376,377,379,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,672,690,691,692,693,694,702,703,707,711,715,720,726,733,737,741,746,750,754,758,762,766,770,776,780,786,790,796,800,805,809,812,816,822,826,832,836,842,845,849,853,857,861,865,866,867,868,871,874,877,880,884,885,886,887,888,891,893,895,897,902,903,907,913,917,918,920,932,933,937,943,947,948,949,953,980,984,985,989,1017,1189,1215,1386,1412,1443,1451,1457,1473,1495,1500,1505,1515,1524,1533,1537,1544,1563,1570,1571,1580,1583,1586,1590,1594,1598,1601,1602,1607,1612,1622,1627,1634,1640,1641,1644,1648,1653,1655,1657,1660,1663,1665,1669,1672,1679,1682,1685,1689,1691,1695,1697,1699,1701,1705,1713,1721,1733,1739,1748,1751,1762,1765,1766,1771,1772,1777,1887,1957,1958,1968,1977,1978,2131,2135,2138,2141,2144,2147,2150,2153,2156,2160,2163,2166,2169,2173,2176,2180,2184,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2206,2208,2209,2210,2211,2212,2213,2214,2215,2217,2218,2220,2221,2223,2225,2226,2228,2229,2230,2231,2232,2233,2235,2236,2237,2238,2239,2240,2284,2286,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2302,2303,2304,2305,2306,2307,2308,2310,2314,2318,2366,2367,2368,2369,2370,2374,2375,2376,2377,2393,2395,2397,2399,2401,2402,2403,2404,2406,2408,2410,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2424,2425,2426,2427,2429,2431,2432,2434,2435,2437,2439,2441,2442,2443,2444,2445,2446,2447,2448,2449,2450,2451,2452,2454,2455,2456,2457,2459,2460,2461,2462,2463,2465,2467,2469,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2571,2574,2577,2580,2594,2600,2627,2662,2691,2718,2727,2791,3154,3163,3208,3246,3284,3416,3422,3428,3470,3594,3614,3667,3671,3817,3873,3898,4012,4066,4121,4133,4159,4166", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,1817,1872,1934,2111,2181,2242,2317,2393,2470,2548,2793,2875,2951,3027,3104,3182,3288,3394,3473,3553,3610,3668,3873,3948,4013,4079,4139,4200,4272,4345,4412,4480,4539,4598,4657,4716,4775,4829,4883,4936,4990,5044,5098,5152,6696,6775,6848,6922,7065,7137,7210,7267,7325,7472,7546,7621,7693,7766,7836,7907,7967,8028,8139,8208,8278,8352,8428,8492,8569,8645,8722,8787,8856,8933,9008,9077,9145,9222,9288,9349,9446,9511,9580,9679,9750,9809,9867,9924,9983,10047,10118,10190,10262,10334,10406,10473,10541,10609,10668,10731,10795,10885,10976,11036,11102,11169,11235,11305,11369,11422,11489,11550,11617,11730,11788,11851,11916,11981,12056,12129,12201,12245,12292,12338,12387,12448,12509,12570,12632,12696,12760,12824,12889,12952,13012,13073,13139,13198,13258,13320,13391,13451,13519,14093,14180,14433,14520,14608,14690,14773,14863,14954,17988,18046,18091,18157,18221,18278,18335,18389,20569,20617,20666,20717,21309,21983,22032,22287,23558,23989,24051,24168,30804,30874,30952,31006,31076,31161,31209,31255,31316,31379,31445,31509,31580,31643,31708,31772,31833,31894,31946,32019,32093,32162,32237,32311,32385,32526,32596,44639,45672,45762,45850,45946,46036,46618,46707,46954,47235,47487,47772,48165,48642,48864,49086,49362,49589,49819,50049,50279,50509,50736,51155,51381,51806,52036,52464,52683,52966,53174,53305,53532,53958,54183,54610,54831,55256,55376,55652,55953,56277,56568,56882,57019,57150,57255,57497,57664,57868,58076,58347,58459,58571,58676,58793,59007,59153,59293,59379,59727,59815,60061,60479,60728,60810,60908,61565,61665,61917,62341,62596,62690,62779,63016,65040,65282,65384,65637,67793,78474,79990,90685,92213,93970,94596,95016,96277,97542,97798,98034,98581,99075,99680,99878,100458,101826,102201,102319,102857,103014,103210,103483,103739,103909,104050,104114,104479,104846,105522,105786,106124,106477,106571,106757,107063,107325,107450,107577,107816,108027,108146,108339,108516,108971,109152,109274,109533,109646,109833,109935,110042,110171,110446,110954,111450,112327,112621,113191,113340,114072,114244,114328,114664,114756,115034,123051,128422,128484,129062,129646,129737,137706,137935,138095,138247,138418,138584,138753,138920,139083,139326,139496,139669,139840,140114,140313,140518,140848,140932,141028,141124,141222,141322,141424,141526,141628,141730,141832,141932,142028,142140,142269,142392,142523,142654,142752,142866,142960,143100,143234,143330,143442,143542,143658,143754,143866,143966,144106,144242,144406,144536,144694,144844,144985,145129,145264,145376,145526,145654,145782,145918,146050,146180,146310,146422,146562,149660,149804,149942,150008,150098,150174,150278,150368,150470,150578,150686,150786,150866,150958,151056,151166,151218,151296,151402,151494,151598,151708,151830,151993,152150,155630,155730,155820,155930,156020,156261,156355,156461,156553,157702,157814,157928,158044,158160,158254,158368,158480,158582,158702,158824,158906,159010,159130,159256,159354,159448,159536,159648,159764,159886,159998,160173,160289,160375,160467,160579,160703,160770,160896,160964,161092,161236,161364,161433,161528,161643,161756,161855,161964,162075,162186,162287,162392,162492,162622,162713,162836,162930,163042,163128,163232,163328,163416,163534,163638,163742,163868,163956,164064,164164,164254,164364,164448,164550,164634,164688,164752,164858,164944,165054,165138,165258,168595,168713,168828,168908,169269,169502,170649,171614,172958,174319,174707,177550,187603,187923,189897,191555,192856,197411,197673,197873,198973,203251,203857,206009,206160,210832,212755,213519,218114,219956,222087,222427,223738,223941"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings_actions.xml", "from": {"startLines": "13,21,3,23,18,16,15,9,4,5,7,10,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "409,677,78,746,605,553,505,276,124,174,224,322,453", "endColumns": "42,65,44,52,44,48,46,44,48,46,48,50,50", "endOffsets": "447,738,118,794,645,597,547,316,168,216,268,368,499"}, "to": {"startLines": "389,393,394,395,396,397,398,399,400,401,402,404,405", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24848,25036,25102,25147,25200,25245,25294,25341,25386,25435,25482,25580,25631", "endColumns": "42,65,44,52,44,48,46,44,48,46,48,50,50", "endOffsets": "24886,25097,25142,25195,25240,25289,25336,25381,25430,25477,25526,25626,25677"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4ec837fe980ac62f82a7402163f3b9e1\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "323", "startColumns": "4", "startOffsets": "21163", "endColumns": "65", "endOffsets": "21224"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "34,101,261,262,263,264,265,266,267,335,336,337,386,387,521,552,661,663,681,687,688,1778,1979,1982,1988,1994,1997,2003,2007,2010,2017,2023,2026,2032,2037,2042,2049,2051,2057,2063,2071,2076,2083,2088,2094,2098,2105,2109,2115,2121,2124,2128,2129,3155,3170,3247,3285,3429,3615,3672,3736,3746,3756,3763,3769,3874,4013,4030", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1939,6558,17508,17572,17627,17695,17762,17827,17884,21784,21832,21880,24652,24715,33424,35829,44020,44112,45029,45452,45502,115039,129742,129847,130092,130430,130576,130916,131128,131291,131698,132036,132159,132498,132737,132994,133365,133425,133763,134049,134498,134790,135178,135483,135827,136072,136402,136609,136877,137150,137294,137495,137542,187608,188131,191560,192861,197878,203862,206165,208090,208372,208677,208939,209199,212760,218119,218649", "endLines": "34,101,261,262,263,264,265,266,267,335,336,337,386,387,521,552,661,665,681,687,688,1794,1981,1987,1993,1996,2002,2006,2009,2016,2022,2025,2031,2036,2041,2048,2050,2056,2062,2070,2075,2082,2087,2093,2097,2104,2108,2114,2120,2123,2127,2128,2129,3159,3180,3266,3288,3438,3622,3735,3745,3755,3762,3768,3811,3886,4029,4046", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2007,6622,17567,17622,17690,17757,17822,17879,17936,21827,21875,21936,24710,24773,33457,35881,44059,44247,45163,45497,45545,116472,129842,130087,130425,130571,130911,131123,131286,131693,132031,132154,132493,132732,132989,133360,133420,133758,134044,134493,134785,135173,135478,135822,136067,136397,136604,136872,137145,137289,137490,137537,137593,187788,188527,192284,193005,198205,204105,208085,208367,208672,208934,209194,210617,213207,218644,219212"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\cc1d3805fe8d4fc761fa3c8cb2574116\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "321,322,333,341,342,363,364,365,366,367", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "21076,21116,21674,22079,22134,23265,23319,23371,23420,23481", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "21111,21158,21712,22129,22176,23314,23366,23415,23476,23526"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\89628933edb8d4035434c63d83660fcd\\transformed\\navigation-common-release\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3623,3636,3642,3648,3657", "startColumns": "4,4,4,4,4", "startOffsets": "204110,204749,204993,205240,205603", "endLines": "3635,3641,3647,3650,3661", "endColumns": "24,24,24,24,24", "endOffsets": "204744,204988,205235,205368,205780"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\6a19f80353fbc6fa60e88fd607d06113\\transformed\\recyclerview-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,38", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,2084"}, "to": {"startLines": "22,213,214,215,223,224,225,328,3818", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1389,14185,14244,14292,14959,15034,15110,21423,210837", "endLines": "22,213,214,215,223,224,225,328,3838", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "1440,14239,14287,14343,15029,15105,15177,21484,211672"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4ff8829df674d3dbd062b8350784bf54\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "320,343,374,3345,3350", "startColumns": "4,4,4,4,4", "startOffsets": "21019,22181,23813,195497,195667", "endLines": "320,343,374,3349,3353", "endColumns": "56,64,63,24,24", "endOffsets": "21071,22241,23872,195662,195811"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "82", "endOffsets": "135"}, "to": {"startLines": "2365", "startColumns": "4", "startOffsets": "155473", "endColumns": "81", "endOffsets": "155550"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\6cddd03f981b66f6053ec06c3046d7ce\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "369", "startColumns": "4", "startOffsets": "23563", "endColumns": "42", "endOffsets": "23601"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "41", "endOffsets": "94"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "8033", "endColumns": "41", "endOffsets": "8070"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\3d2aea75d7f968093da044482f8492b7\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2628,2644,2650,3927,3943", "startColumns": "4,4,4,4,4", "startOffsets": "170654,171079,171257,214555,214966", "endLines": "2643,2649,2659,3942,3946", "endColumns": "24,24,24,24,24", "endOffsets": "171074,171252,171536,214961,215088"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c7890e83d7f221a44550e26073b9aa79\\transformed\\coil-base-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "317", "startColumns": "4", "startOffsets": "20856", "endColumns": "49", "endOffsets": "20901"}}]}]}