{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values/values.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "493,667,668", "startColumns": "4,4,4", "startOffsets": "31642,44235,44291", "endColumns": "45,55,54", "endOffsets": "31683,44286,44341"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\acf97b938bacc48adeb9122891289346\\transformed\\window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1"}, "to": {"startLines": "23,28,29,30,316", "startColumns": "4,4,4,4,4", "startOffsets": "1445,1624,1684,1736,20796", "endLines": "27,28,29,30,316", "endColumns": "11,59,51,44,59", "endOffsets": "1619,1679,1731,1776,20851"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\754763af6e012e597e57d1d6e7d6d983\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "373", "startColumns": "4", "startOffsets": "23763", "endColumns": "49", "endOffsets": "23808"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\ae0358a2c93f2661aabc6c05923ecb92\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "370", "startColumns": "4", "startOffsets": "23606", "endColumns": "42", "endOffsets": "23644"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "525,526,527,528,529,530,531,532,533", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "34077,34147,34209,34274,34338,34415,34480,34570,34654", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "34142,34204,34269,34333,34410,34475,34565,34649,34718"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "100,106,112,331,378,505,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,600,601,602,603,604,605,2228,2230,2231,2236,2238", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6469,6927,7330,21559,24056,32422,32655,32744,32817,32913,33008,33091,33175,33256,33341,33421,33485,33579,33658,33752,33826,33917,33989,39477,39545,39611,39687,39769,39856,145779,145955,146077,146339,146534", "endColumns": "88,70,72,61,59,73,88,72,95,94,82,83,80,84,79,63,93,78,93,73,90,71,87,67,65,75,81,86,94,65,121,60,65,66", "endOffsets": "6553,6993,7398,21616,24111,32491,32739,32812,32908,33003,33086,33170,33251,33336,33416,33480,33574,33653,33747,33821,33912,33984,34072,39540,39606,39682,39764,39851,39946,145840,146072,146133,146400,146596"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "402,403,492,644,648,656,658", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "25488,25530,31578,42958,43146,43584,43684", "endColumns": "41,43,63,47,49,45,39", "endOffsets": "25525,25569,31637,43001,43191,43625,43719"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings_screens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "439,440,606,649,652,653,671", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "27995,28054,39951,43196,43367,43425,44444", "endColumns": "58,54,51,66,57,59,48", "endOffsets": "28049,28104,39998,43258,43420,43480,44488"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\7109a23afcec01fecc6451570c536a6e\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "340,371", "startColumns": "4,4", "startOffsets": "22037,23649", "endColumns": "41,59", "endOffsets": "22074,23704"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "380,538,539,540,541,542,543,544,545,546,547,550,551,552,553,554,555,556,557,558,559,560,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,1780,1790", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24173,34978,35066,35152,35233,35317,35386,35451,35534,35640,35726,35846,35900,35969,36030,36099,36188,36283,36357,36454,36547,36645,36794,36885,36973,37069,37167,37231,37299,37386,37480,37547,37619,37691,37792,37901,37977,38046,38094,38160,38224,38298,38355,38412,38484,38534,38588,38659,38730,38800,38869,38927,39003,39074,39148,39234,39284,39354,115483,116198", "endLines": "380,538,539,540,541,542,543,544,545,546,549,550,551,552,553,554,555,556,557,558,559,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,1789,1792", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "24241,35061,35147,35228,35312,35381,35446,35529,35635,35721,35841,35895,35964,36025,36094,36183,36278,36352,36449,36542,36640,36789,36880,36968,37064,37162,37226,37294,37381,37475,37542,37614,37686,37787,37896,37972,38041,38089,38155,38219,38293,38350,38407,38479,38529,38583,38654,38725,38795,38864,38922,38998,39069,39143,39229,39279,39349,39414,116193,116346"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2ef59a3900bc6a0162f77e6c6369129b\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "372", "startColumns": "4", "startOffsets": "23709", "endColumns": "53", "endOffsets": "23758"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "21,43,44,57,58,98,99,204,205,206,207,208,209,210,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,329,330,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,388,494,495,496,497,498,499,500,662,2223,2224,2229,2232,2237,2469,2470", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1329,2553,2625,3673,3738,6337,6406,13524,13594,13662,13734,13804,13865,13939,16530,16591,16652,16714,16778,16840,16901,16969,17069,17129,17195,17268,17337,17394,17446,18394,18466,18542,18607,18666,18725,18785,18845,18905,18965,19025,19085,19145,19205,19265,19325,19384,19444,19504,19564,19624,19684,19744,19804,19864,19924,19984,20043,20103,20163,20222,20281,20340,20399,20458,21489,21524,22292,22347,22410,22465,22523,22579,22637,22698,22761,22818,22869,22927,22977,23038,23095,23161,23195,23230,24778,31688,31755,31827,31896,31965,32039,32111,43852,145461,145578,145845,146138,146405,164157,164229", "endLines": "21,43,44,57,58,98,99,204,205,206,207,208,209,210,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,329,330,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,388,494,495,496,497,498,499,500,662,2223,2227,2229,2235,2237,2469,2470", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "1384,2620,2708,3733,3799,6401,6464,13589,13657,13729,13799,13860,13934,14007,16586,16647,16709,16773,16835,16896,16964,17064,17124,17190,17263,17332,17389,17441,17503,18461,18537,18602,18661,18720,18780,18840,18900,18960,19020,19080,19140,19200,19260,19320,19379,19439,19499,19559,19619,19679,19739,19799,19859,19919,19979,20038,20098,20158,20217,20276,20335,20394,20453,20512,21519,21554,22342,22405,22460,22518,22574,22632,22693,22756,22813,22864,22922,22972,23033,23090,23156,23190,23225,23260,24843,31750,31822,31891,31960,32034,32106,32194,43918,145573,145774,145950,146334,146529,164224,164291"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,35,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,381,382,383,384,385,535,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,2239,2244,2247,2253,2256,2259,2262,2263,2264,2301,2321,2341,2344,2360,2367,2471,2475", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,366,433,493,555,628,696,753,822,884,945,1018,1086,1147,1201,1264,2012,5157,5218,5284,5350,5420,5491,5569,5648,5711,5775,5843,5912,5987,6063,6125,6188,6262,15182,15258,15331,15406,15479,15545,15598,15661,15727,15784,15859,15937,16012,16087,16147,16207,16267,16333,16398,16458,24246,24331,24419,24505,24592,34780,40003,40066,40135,40206,40281,40365,40447,40512,40565,40710,40846,40986,41128,41262,41405,41545,41629,41691,41763,41829,41895,41959,42021,42099,42177,42231,42283,42335,42403,42473,42536,42604,42678,42734,146601,146944,147116,147516,147691,147904,148118,148219,148320,151049,152526,154025,154198,155452,155973,164296,164594", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,35,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,381,382,383,384,385,535,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,2243,2246,2252,2255,2258,2261,2262,2263,2264,2320,2340,2343,2346,2366,2373,2474,2478", "endColumns": "66,66,59,61,72,67,56,68,61,60,72,67,60,53,62,64,39,60,65,65,69,70,77,78,62,63,67,68,74,75,61,62,73,74,75,72,74,72,65,52,62,65,56,74,77,74,74,59,59,59,65,64,59,71,84,87,85,86,59,85,62,68,70,74,83,81,64,52,144,135,139,141,133,142,139,83,61,71,65,65,63,61,77,77,53,51,51,67,69,62,67,73,55,63,12,12,12,12,12,12,100,100,92,12,12,12,12,12,12,12,12", "endOffsets": "361,428,488,550,623,691,748,817,879,940,1013,1081,1142,1196,1259,1324,2047,5213,5279,5345,5415,5486,5564,5643,5706,5770,5838,5907,5982,6058,6120,6183,6257,6332,15253,15326,15401,15474,15540,15593,15656,15722,15779,15854,15932,16007,16082,16142,16202,16262,16328,16393,16453,16525,24326,24414,24500,24587,24647,34861,40061,40130,40201,40276,40360,40442,40507,40560,40705,40841,40981,41123,41257,41400,41540,41624,41686,41758,41824,41890,41954,42016,42094,42172,42226,42278,42330,42398,42468,42531,42599,42673,42729,42793,146939,147111,147511,147686,147899,148113,148214,148315,148408,152521,154020,154193,154362,155968,156496,164589,164873"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\e8868848eae7ccc45186e654e202dd67\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "491", "startColumns": "4", "startOffsets": "31495", "endColumns": "82", "endOffsets": "31573"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\902f73074689cc7f7e9e84c151d54f0b\\transformed\\navigation-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "332", "startColumns": "4", "startOffsets": "21621", "endColumns": "52", "endOffsets": "21669"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings_settings.xml", "from": {"startLines": "19,-1,18,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,17,-1,-1,-1", "startColumns": "8,-1,8,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8,-1,-1,-1", "startOffsets": "1323,-1,1255,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,1176,-1,-1,-1", "endColumns": "66,-1,66,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,77,-1,-1,-1", "endOffsets": "1381,-1,1313,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,1245,-1,-1,-1"}, "to": {"startLines": "441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28109,28172,28236,28299,28379,28439,28509,28579,28655,28731,28804,28882,28942,29021,29093,29167,29233,29299", "endColumns": "62,63,62,79,59,69,69,75,75,72,77,59,78,71,73,65,65,61", "endOffsets": "28167,28231,28294,28374,28434,28504,28574,28650,28726,28799,28877,28937,29016,29088,29162,29228,29294,29356"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "599", "startColumns": "4", "startOffsets": "39419", "endColumns": "57", "endOffsets": "39472"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings_dialogs.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,459,460,461,462,463", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25574,25627,25679,25730,25794,25863,25931,25998,26059,26115,26176,26223,26282,26364,26469,26530,26594,26657,26720,26783,26857,26920,26994,27063,27120,27175,27225,27299,27370,27505,27594,27698,27776,27847,27930,29361,29415,29491,29535,29581", "endColumns": "52,51,50,63,68,67,66,60,55,60,46,58,81,104,60,63,62,62,62,73,62,73,68,56,54,49,73,70,134,88,103,77,70,82,64,53,75,43,45,47", "endOffsets": "25622,25674,25725,25789,25858,25926,25993,26054,26110,26171,26218,26277,26359,26464,26525,26589,26652,26715,26778,26852,26915,26989,27058,27115,27170,27220,27294,27365,27500,27589,27693,27771,27842,27925,27990,29410,29486,29530,29576,29624"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\adbf5b882905ad8bd23ebd88774c480b\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "327,334", "startColumns": "4,4", "startOffsets": "21369,21717", "endColumns": "53,66", "endOffsets": "21418,21779"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "315,318,319,324,326,375,501,502,504,506,507,536,537,641,642,650,651,655,657,659,660,661,664,665,666,1777,1793,1796", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20722,20906,20964,21229,21314,23877,32199,32264,32356,32496,32597,34866,34918,42798,42860,43263,43313,43538,43630,43724,43770,43812,44062,44109,44145,115371,116351,116462", "endLines": "315,318,319,324,326,375,501,502,504,506,507,536,537,641,642,650,651,655,657,659,660,661,664,665,666,1779,1795,1800", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "20791,20959,21014,21275,21364,23925,32259,32313,32417,32592,32650,34913,34973,42855,42909,43308,43362,43579,43679,43765,43807,43847,44104,44140,44230,115478,116457,116714"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,31,32,33,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,102,103,104,105,107,108,109,110,111,113,114,115,116,117,118,119,120,121,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,211,212,216,217,218,219,220,221,222,268,269,270,271,272,273,274,275,311,312,313,314,325,338,339,344,368,376,377,379,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,654,672,673,674,675,676,677,685,686,690,694,698,703,709,716,720,724,729,733,737,741,745,749,753,759,763,769,773,779,783,788,792,795,799,805,809,815,819,825,828,832,836,840,844,848,849,850,851,854,857,860,863,867,868,869,870,871,874,876,878,880,885,886,890,896,900,901,903,915,916,920,926,930,931,932,936,963,967,968,972,1000,1172,1198,1369,1395,1426,1434,1440,1456,1478,1483,1488,1498,1507,1516,1520,1527,1546,1553,1554,1563,1566,1569,1573,1577,1581,1584,1585,1590,1595,1605,1610,1617,1623,1624,1627,1631,1636,1638,1640,1643,1646,1648,1652,1655,1662,1665,1668,1672,1674,1678,1680,1682,1684,1688,1696,1704,1716,1722,1731,1734,1745,1748,1749,1754,1755,1801,1870,1940,1941,1951,1960,2112,2114,2118,2121,2124,2127,2130,2133,2136,2139,2143,2146,2149,2152,2156,2159,2163,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2189,2191,2192,2193,2194,2195,2196,2197,2198,2200,2201,2203,2204,2206,2208,2209,2211,2212,2213,2214,2215,2216,2218,2219,2220,2221,2222,2265,2267,2269,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2285,2286,2287,2288,2289,2290,2291,2293,2297,2348,2349,2350,2351,2352,2353,2357,2358,2359,2374,2376,2378,2380,2382,2384,2385,2386,2387,2389,2391,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2407,2408,2409,2410,2412,2414,2415,2417,2418,2420,2422,2424,2425,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2437,2438,2439,2440,2442,2443,2444,2445,2446,2448,2450,2452,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,1781,1822,1877,2052,2116,2186,2247,2322,2398,2475,2713,2798,2880,2956,3032,3109,3187,3293,3399,3478,3558,3615,3804,3878,3953,4018,4084,4144,4205,4277,4350,4417,4485,4544,4603,4662,4721,4780,4834,4888,4941,4995,5049,5103,6627,6701,6780,6853,6998,7070,7142,7215,7272,7403,7477,7551,7626,7698,7771,7841,7912,7972,8075,8144,8213,8283,8357,8433,8497,8574,8650,8727,8792,8861,8938,9013,9082,9150,9227,9293,9354,9451,9516,9585,9684,9755,9814,9872,9929,9988,10052,10123,10195,10267,10339,10411,10478,10546,10614,10673,10736,10800,10890,10981,11041,11107,11174,11240,11310,11374,11427,11494,11555,11622,11735,11793,11856,11921,11986,12061,12134,12206,12250,12297,12343,12392,12453,12514,12575,12637,12701,12765,12829,12894,12957,13017,13078,13144,13203,13263,13325,13396,13456,14012,14098,14348,14438,14525,14613,14695,14778,14868,17941,17993,18051,18096,18162,18226,18283,18340,20517,20574,20622,20671,21280,21941,21988,22246,23531,23930,23994,24116,29629,29703,29773,29851,29905,29975,30060,30108,30154,30215,30278,30344,30408,30479,30542,30607,30671,30732,30793,30845,30918,30992,31061,31136,31210,31284,31425,43485,44493,44571,44661,44749,44845,44935,45517,45606,45853,46134,46386,46671,47064,47541,47763,47985,48261,48488,48718,48948,49178,49408,49635,50054,50280,50705,50935,51363,51582,51865,52073,52204,52431,52857,53082,53509,53730,54155,54275,54551,54852,55176,55467,55781,55918,56049,56154,56396,56563,56767,56975,57246,57358,57470,57575,57692,57906,58052,58192,58278,58626,58714,58960,59378,59627,59709,59807,60464,60564,60816,61240,61495,61589,61678,61915,63939,64181,64283,64536,66692,77373,78889,89584,91112,92869,93495,93915,95176,96441,96697,96933,97480,97974,98579,98777,99357,100725,101100,101218,101756,101913,102109,102382,102638,102808,102949,103013,103378,103745,104421,104685,105023,105376,105470,105656,105962,106224,106349,106476,106715,106926,107045,107238,107415,107870,108051,108173,108432,108545,108732,108834,108941,109070,109345,109853,110349,111226,111520,112090,112239,112971,113143,113227,113563,113655,116719,121950,127321,127383,127961,128545,136492,136605,136834,136994,137146,137317,137483,137652,137819,137982,138225,138395,138568,138739,139013,139212,139417,139747,139831,139927,140023,140121,140221,140323,140425,140527,140629,140731,140831,140927,141039,141168,141291,141422,141553,141651,141765,141859,141999,142133,142229,142341,142441,142557,142653,142765,142865,143005,143141,143305,143435,143593,143743,143884,144028,144163,144275,144425,144553,144681,144817,144949,145079,145209,145321,148413,148559,148703,148841,148907,148997,149073,149177,149267,149369,149477,149585,149685,149765,149857,149955,150065,150117,150195,150301,150393,150497,150607,150729,150892,154449,154529,154629,154719,154829,154919,155160,155254,155360,156501,156601,156713,156827,156943,157059,157153,157267,157379,157481,157601,157723,157805,157909,158029,158155,158253,158347,158435,158547,158663,158785,158897,159072,159188,159274,159366,159478,159602,159669,159795,159863,159991,160135,160263,160332,160427,160542,160655,160754,160863,160974,161085,161186,161291,161391,161521,161612,161735,161829,161941,162027,162131,162227,162315,162433,162537,162641,162767,162855,162963,163063,163153,163263,163347,163449,163533,163587,163651,163757,163843,163953,164037", "endLines": "2,3,4,31,32,33,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,102,103,104,105,107,108,109,110,111,113,114,115,116,117,118,119,120,121,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,211,212,216,217,218,219,220,221,222,268,269,270,271,272,273,274,275,311,312,313,314,325,338,339,344,368,376,377,379,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,654,672,673,674,675,676,684,685,689,693,697,702,708,715,719,723,728,732,736,740,744,748,752,758,762,768,772,778,782,787,791,794,798,804,808,814,818,824,827,831,835,839,843,847,848,849,850,853,856,859,862,866,867,868,869,870,873,875,877,879,884,885,889,895,899,900,902,914,915,919,925,929,930,931,935,962,966,967,971,999,1171,1197,1368,1394,1425,1433,1439,1455,1477,1482,1487,1497,1506,1515,1519,1526,1545,1552,1553,1562,1565,1568,1572,1576,1580,1583,1584,1589,1594,1604,1609,1616,1622,1623,1626,1630,1635,1637,1639,1642,1645,1647,1651,1654,1661,1664,1667,1671,1673,1677,1679,1681,1683,1687,1695,1703,1715,1721,1730,1733,1744,1747,1748,1753,1754,1759,1869,1939,1940,1950,1959,1960,2113,2117,2120,2123,2126,2129,2132,2135,2138,2142,2145,2148,2151,2155,2158,2162,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2188,2190,2191,2192,2193,2194,2195,2196,2197,2199,2200,2202,2203,2205,2207,2208,2210,2211,2212,2213,2214,2215,2217,2218,2219,2220,2221,2222,2266,2268,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2284,2285,2286,2287,2288,2289,2290,2292,2296,2300,2348,2349,2350,2351,2352,2356,2357,2358,2359,2375,2377,2379,2381,2383,2384,2385,2386,2388,2390,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2406,2407,2408,2409,2411,2413,2414,2416,2417,2419,2421,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2433,2434,2436,2437,2438,2439,2441,2442,2443,2444,2445,2447,2449,2451,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "200,245,294,1817,1872,1934,2111,2181,2242,2317,2393,2470,2548,2793,2875,2951,3027,3104,3182,3288,3394,3473,3553,3610,3668,3873,3948,4013,4079,4139,4200,4272,4345,4412,4480,4539,4598,4657,4716,4775,4829,4883,4936,4990,5044,5098,5152,6696,6775,6848,6922,7065,7137,7210,7267,7325,7472,7546,7621,7693,7766,7836,7907,7967,8028,8139,8208,8278,8352,8428,8492,8569,8645,8722,8787,8856,8933,9008,9077,9145,9222,9288,9349,9446,9511,9580,9679,9750,9809,9867,9924,9983,10047,10118,10190,10262,10334,10406,10473,10541,10609,10668,10731,10795,10885,10976,11036,11102,11169,11235,11305,11369,11422,11489,11550,11617,11730,11788,11851,11916,11981,12056,12129,12201,12245,12292,12338,12387,12448,12509,12570,12632,12696,12760,12824,12889,12952,13012,13073,13139,13198,13258,13320,13391,13451,13519,14093,14180,14433,14520,14608,14690,14773,14863,14954,17988,18046,18091,18157,18221,18278,18335,18389,20569,20617,20666,20717,21309,21983,22032,22287,23558,23989,24051,24168,29698,29768,29846,29900,29970,30055,30103,30149,30210,30273,30339,30403,30474,30537,30602,30666,30727,30788,30840,30913,30987,31056,31131,31205,31279,31420,31490,43533,44566,44656,44744,44840,44930,45512,45601,45848,46129,46381,46666,47059,47536,47758,47980,48256,48483,48713,48943,49173,49403,49630,50049,50275,50700,50930,51358,51577,51860,52068,52199,52426,52852,53077,53504,53725,54150,54270,54546,54847,55171,55462,55776,55913,56044,56149,56391,56558,56762,56970,57241,57353,57465,57570,57687,57901,58047,58187,58273,58621,58709,58955,59373,59622,59704,59802,60459,60559,60811,61235,61490,61584,61673,61910,63934,64176,64278,64531,66687,77368,78884,89579,91107,92864,93490,93910,95171,96436,96692,96928,97475,97969,98574,98772,99352,100720,101095,101213,101751,101908,102104,102377,102633,102803,102944,103008,103373,103740,104416,104680,105018,105371,105465,105651,105957,106219,106344,106471,106710,106921,107040,107233,107410,107865,108046,108168,108427,108540,108727,108829,108936,109065,109340,109848,110344,111221,111515,112085,112234,112966,113138,113222,113558,113650,113928,121945,127316,127378,127956,128540,128631,136600,136829,136989,137141,137312,137478,137647,137814,137977,138220,138390,138563,138734,139008,139207,139412,139742,139826,139922,140018,140116,140216,140318,140420,140522,140624,140726,140826,140922,141034,141163,141286,141417,141548,141646,141760,141854,141994,142128,142224,142336,142436,142552,142648,142760,142860,143000,143136,143300,143430,143588,143738,143879,144023,144158,144270,144420,144548,144676,144812,144944,145074,145204,145316,145456,148554,148698,148836,148902,148992,149068,149172,149262,149364,149472,149580,149680,149760,149852,149950,150060,150112,150190,150296,150388,150492,150602,150724,150887,151044,154524,154624,154714,154824,154914,155155,155249,155355,155447,156596,156708,156822,156938,157054,157148,157262,157374,157476,157596,157718,157800,157904,158024,158150,158248,158342,158430,158542,158658,158780,158892,159067,159183,159269,159361,159473,159597,159664,159790,159858,159986,160130,160258,160327,160422,160537,160650,160749,160858,160969,161080,161181,161286,161386,161516,161607,161730,161824,161936,162022,162126,162222,162310,162428,162532,162636,162762,162850,162958,163058,163148,163258,163342,163444,163528,163582,163646,163752,163838,163948,164032,164152"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings_actions.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "389,390,391,392,393,394,395,396,397,398,399,400,401", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24848,24891,24957,25002,25055,25100,25149,25196,25241,25290,25337,25386,25437", "endColumns": "42,65,44,52,44,48,46,44,48,46,48,50,50", "endOffsets": "24886,24952,24997,25050,25095,25144,25191,25236,25285,25332,25381,25432,25483"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4ec837fe980ac62f82a7402163f3b9e1\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "323", "startColumns": "4", "startOffsets": "21163", "endColumns": "65", "endOffsets": "21224"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "34,101,261,262,263,264,265,266,267,335,336,337,386,387,503,534,643,645,663,669,670,1760,1961,1964,1970,1976,1979,1985,1989,1992,1999,2005,2008,2014,2019,2024,2031,2033,2039,2045,2053,2058,2065,2070,2076,2080,2087,2091,2097,2103,2106,2110,2111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1939,6558,17508,17572,17627,17695,17762,17827,17884,21784,21832,21880,24652,24715,32318,34723,42914,43006,43923,44346,44396,113933,128636,128741,128986,129324,129470,129810,130022,130185,130592,130930,131053,131392,131631,131888,132259,132319,132657,132943,133392,133684,134072,134377,134721,134966,135296,135503,135771,136044,136188,136389,136436", "endLines": "34,101,261,262,263,264,265,266,267,335,336,337,386,387,503,534,643,647,663,669,670,1776,1963,1969,1975,1978,1984,1988,1991,1998,2004,2007,2013,2018,2023,2030,2032,2038,2044,2052,2057,2064,2069,2075,2079,2086,2090,2096,2102,2105,2109,2110,2111", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55", "endOffsets": "2007,6622,17567,17622,17690,17757,17822,17879,17936,21827,21875,21936,24710,24773,32351,34775,42953,43141,44057,44391,44439,115366,128736,128981,129319,129465,129805,130017,130180,130587,130925,131048,131387,131626,131883,132254,132314,132652,132938,133387,133679,134067,134372,134716,134961,135291,135498,135766,136039,136183,136384,136431,136487"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\cc1d3805fe8d4fc761fa3c8cb2574116\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "321,322,333,341,342,363,364,365,366,367", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "21076,21116,21674,22079,22134,23265,23319,23371,23420,23481", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "21111,21158,21712,22129,22176,23314,23366,23415,23476,23526"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\6a19f80353fbc6fa60e88fd607d06113\\transformed\\recyclerview-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "22,213,214,215,223,224,225,328", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1389,14185,14244,14292,14959,15034,15110,21423", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "1440,14239,14287,14343,15029,15105,15177,21484"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4ff8829df674d3dbd062b8350784bf54\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "320,343,374", "startColumns": "4,4,4", "startOffsets": "21019,22181,23813", "endColumns": "56,64,63", "endOffsets": "21071,22241,23872"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2347", "startColumns": "4", "startOffsets": "154367", "endColumns": "81", "endOffsets": "154444"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\6cddd03f981b66f6053ec06c3046d7ce\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "369", "startColumns": "4", "startOffsets": "23563", "endColumns": "42", "endOffsets": "23601"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "8033", "endColumns": "41", "endOffsets": "8070"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c7890e83d7f221a44550e26073b9aa79\\transformed\\coil-base-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "317", "startColumns": "4", "startOffsets": "20856", "endColumns": "49", "endOffsets": "20901"}}]}, {"outputFile": "E:\\android_development_cache\\.gradle\\daemon\\8.11.1\\com.craftworks.music.app-mergeDebugResources-3:\\values\\values.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,101,157", "endColumns": "45,55,54", "endOffsets": "96,152,207"}, "to": {"startLines": "490,664,665", "startColumns": "4,4,4", "startOffsets": "31442,44035,44091", "endColumns": "45,55,54", "endOffsets": "31483,44086,44141"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\acf97b938bacc48adeb9122891289346\\transformed\\window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,7,8,9,10,11,19,23,34,51", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,234,294,346,391,451,869,1066,1790,2904", "endLines": "6,7,8,9,10,18,22,33,50,58", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "229,289,341,386,446,864,1061,1785,2899,3292"}, "to": {"startLines": "23,28,29,30,316,2580,2593,3878,3886,3898", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "1445,1624,1684,1736,20796,168201,168700,212218,212500,212940", "endLines": "27,28,29,30,316,2585,2596,3885,3897,3905", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "1619,1679,1731,1776,20851,168391,168826,212495,212935,213244"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\754763af6e012e597e57d1d6e7d6d983\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "373", "startColumns": "4", "startOffsets": "23763", "endColumns": "49", "endOffsets": "23808"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\ae0358a2c93f2661aabc6c05923ecb92\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "370", "startColumns": "4", "startOffsets": "23606", "endColumns": "42", "endOffsets": "23644"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "522,523,524,525,526,527,528,529,530", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "33877,33947,34009,34074,34138,34215,34280,34370,34454", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "33942,34004,34069,34133,34210,34275,34365,34449,34518"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,387,449,509,583,672,745,841,936,1019,1103,1184,1269,1349,1413,1507,1586,1680,1754,1845,1917,2005,2073,2139,2215,2297,2384,2479,2545,2667,2728,2794", "endColumns": "88,70,72,61,59,73,88,72,95,94,82,83,80,84,79,63,93,78,93,73,90,71,87,67,65,75,81,86,94,65,121,60,65,66", "endOffsets": "139,210,283,444,504,578,667,740,836,931,1014,1098,1179,1264,1344,1408,1502,1581,1675,1749,1840,1912,2000,2068,2134,2210,2292,2379,2474,2540,2662,2723,2789,2856"}, "to": {"startLines": "100,106,112,331,378,502,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,597,598,599,600,601,602,2225,2227,2228,2233,2235", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6469,6927,7330,21559,24056,32222,32455,32544,32617,32713,32808,32891,32975,33056,33141,33221,33285,33379,33458,33552,33626,33717,33789,39277,39345,39411,39487,39569,39656,145579,145755,145877,146139,146334", "endColumns": "88,70,72,61,59,73,88,72,95,94,82,83,80,84,79,63,93,78,93,73,90,71,87,67,65,75,81,86,94,65,121,60,65,66", "endOffsets": "6553,6993,7398,21616,24111,32291,32539,32612,32708,32803,32886,32970,33051,33136,33216,33280,33374,33453,33547,33621,33712,33784,33872,39340,39406,39482,39564,39651,39746,145640,145872,145933,146200,146396"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "4,5,1,8,7,6,3", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "148,191,17,334,283,236,107", "endColumns": "41,43,63,47,49,45,39", "endOffsets": "185,230,76,377,328,277,142"}, "to": {"startLines": "402,403,489,641,645,653,655", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "25488,25530,31378,42758,42946,43384,43484", "endColumns": "41,43,63,47,49,45,39", "endOffsets": "25525,25569,31437,42801,42991,43425,43519"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings_screens.xml", "from": {"startLines": "10,11,5,7,4,6,3", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "398,458,185,299,126,238,76", "endColumns": "58,54,51,66,57,59,48", "endOffsets": "452,508,232,361,179,293,120"}, "to": {"startLines": "439,440,603,646,649,650,668", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "27995,28054,39751,42996,43167,43225,44244", "endColumns": "58,54,51,66,57,59,48", "endOffsets": "28049,28104,39798,43058,43220,43280,44288"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\7109a23afcec01fecc6451570c536a6e\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "340,371", "startColumns": "4,4", "startOffsets": "22037,23649", "endColumns": "41,59", "endOffsets": "22074,23704"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "380,535,536,537,538,539,540,541,542,543,544,547,548,549,550,551,552,553,554,555,556,557,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,1777,1787", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24173,34778,34866,34952,35033,35117,35186,35251,35334,35440,35526,35646,35700,35769,35830,35899,35988,36083,36157,36254,36347,36445,36594,36685,36773,36869,36967,37031,37099,37186,37280,37347,37419,37491,37592,37701,37777,37846,37894,37960,38024,38098,38155,38212,38284,38334,38388,38459,38530,38600,38669,38727,38803,38874,38948,39034,39084,39154,115283,115998", "endLines": "380,535,536,537,538,539,540,541,542,543,546,547,548,549,550,551,552,553,554,555,556,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,1786,1789", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "24241,34861,34947,35028,35112,35181,35246,35329,35435,35521,35641,35695,35764,35825,35894,35983,36078,36152,36249,36342,36440,36589,36680,36768,36864,36962,37026,37094,37181,37275,37342,37414,37486,37587,37696,37772,37841,37889,37955,38019,38093,38150,38207,38279,38329,38383,38454,38525,38595,38664,38722,38798,38869,38943,39029,39079,39149,39214,115993,116146"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2ef59a3900bc6a0162f77e6c6369129b\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "372", "startColumns": "4", "startOffsets": "23709", "endColumns": "53", "endOffsets": "23758"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "21,43,44,57,58,98,99,204,205,206,207,208,209,210,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,329,330,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,388,491,492,493,494,495,496,497,659,2220,2221,2226,2229,2234,2466,2467,3143,3188,3268,3303,3333,3366", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1329,2553,2625,3673,3738,6337,6406,13524,13594,13662,13734,13804,13865,13939,16530,16591,16652,16714,16778,16840,16901,16969,17069,17129,17195,17268,17337,17394,17446,18394,18466,18542,18607,18666,18725,18785,18845,18905,18965,19025,19085,19145,19205,19265,19325,19384,19444,19504,19564,19624,19684,19744,19804,19864,19924,19984,20043,20103,20163,20222,20281,20340,20399,20458,21489,21524,22292,22347,22410,22465,22523,22579,22637,22698,22761,22818,22869,22927,22977,23038,23095,23161,23195,23230,24778,31488,31555,31627,31696,31765,31839,31911,43652,145261,145378,145645,145938,146205,163957,164029,186622,188596,191704,193510,194510,195192", "endLines": "21,43,44,57,58,98,99,204,205,206,207,208,209,210,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,329,330,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,388,491,492,493,494,495,496,497,659,2220,2224,2226,2232,2234,2466,2467,3148,3197,3302,3323,3365,3371", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1384,2620,2708,3733,3799,6401,6464,13589,13657,13729,13799,13860,13934,14007,16586,16647,16709,16773,16835,16896,16964,17064,17124,17190,17263,17332,17389,17441,17503,18461,18537,18602,18661,18720,18780,18840,18900,18960,19020,19080,19140,19200,19260,19320,19379,19439,19499,19559,19619,19679,19739,19799,19859,19919,19979,20038,20098,20158,20217,20276,20335,20394,20453,20512,21519,21554,22342,22405,22460,22518,22574,22632,22693,22756,22813,22864,22922,22972,23033,23090,23156,23190,23225,23260,24843,31550,31622,31691,31760,31834,31906,31994,43718,145373,145574,145750,146134,146329,164024,164091,186820,188892,193505,194186,195187,195354"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,101,104,110,113,116,119,120,121,122,142,162,165,168,175,182,186,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,189,249,311,384,452,509,578,640,701,774,842,903,957,1020,1085,1125,1186,1252,1318,1388,1459,1537,1616,1679,1743,1811,1880,1955,2031,2093,2156,2230,2305,2381,2454,2529,2602,2668,2721,2784,2850,2907,2982,3060,3135,3210,3270,3330,3390,3456,3521,3581,3653,3738,3826,3912,3999,4059,4145,4208,4277,4348,4423,4507,4589,4654,4707,4852,4988,5128,5270,5404,5547,5687,5771,5833,5905,5971,6037,6101,6163,6241,6319,6373,6425,6477,6545,6615,6678,6746,6820,6876,6940,7283,7455,7855,8030,8243,8457,8558,8659,8752,10229,11728,11901,12070,12591,13119,13417,13701", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,100,103,109,112,115,118,119,120,121,141,161,164,167,174,181,185,189,200", "endColumns": "66,66,59,61,72,67,56,68,61,60,72,67,60,53,62,64,39,60,65,65,69,70,77,78,62,63,67,68,74,75,61,62,73,74,75,72,74,72,65,52,62,65,56,74,77,74,74,59,59,59,65,64,59,71,84,87,85,86,59,85,62,68,70,74,83,81,64,52,144,135,139,141,133,142,139,83,61,71,65,65,63,61,77,77,53,51,51,67,69,62,67,73,55,63,12,12,12,12,12,12,100,100,92,12,12,12,12,12,12,12,12,24", "endOffsets": "117,184,244,306,379,447,504,573,635,696,769,837,898,952,1015,1080,1120,1181,1247,1313,1383,1454,1532,1611,1674,1738,1806,1875,1950,2026,2088,2151,2225,2300,2376,2449,2524,2597,2663,2716,2779,2845,2902,2977,3055,3130,3205,3265,3325,3385,3451,3516,3576,3648,3733,3821,3907,3994,4054,4140,4203,4272,4343,4418,4502,4584,4649,4702,4847,4983,5123,5265,5399,5542,5682,5766,5828,5900,5966,6032,6096,6158,6236,6314,6368,6420,6472,6540,6610,6673,6741,6815,6871,6935,7278,7450,7850,8025,8238,8452,8553,8654,8747,10224,11723,11896,12065,12586,13114,13412,13696,14085"}, "to": {"startLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,35,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,381,382,383,384,385,532,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,2236,2241,2244,2250,2253,2256,2259,2260,2261,2298,2318,2338,2341,2357,2364,2468,2472,3418", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,366,433,493,555,628,696,753,822,884,945,1018,1086,1147,1201,1264,2012,5157,5218,5284,5350,5420,5491,5569,5648,5711,5775,5843,5912,5987,6063,6125,6188,6262,15182,15258,15331,15406,15479,15545,15598,15661,15727,15784,15859,15937,16012,16087,16147,16207,16267,16333,16398,16458,24246,24331,24419,24505,24592,34580,39803,39866,39935,40006,40081,40165,40247,40312,40365,40510,40646,40786,40928,41062,41205,41345,41429,41491,41563,41629,41695,41759,41821,41899,41977,42031,42083,42135,42203,42273,42336,42404,42478,42534,146401,146744,146916,147316,147491,147704,147918,148019,148120,150849,152326,153825,153998,155252,155773,164096,164394,196904", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,35,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,381,382,383,384,385,532,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,2240,2243,2249,2252,2255,2258,2259,2260,2261,2317,2337,2340,2343,2363,2370,2471,2475,3428", "endColumns": "66,66,59,61,72,67,56,68,61,60,72,67,60,53,62,64,39,60,65,65,69,70,77,78,62,63,67,68,74,75,61,62,73,74,75,72,74,72,65,52,62,65,56,74,77,74,74,59,59,59,65,64,59,71,84,87,85,86,59,85,62,68,70,74,83,81,64,52,144,135,139,141,133,142,139,83,61,71,65,65,63,61,77,77,53,51,51,67,69,62,67,73,55,63,12,12,12,12,12,12,100,100,92,12,12,12,12,12,12,12,12,24", "endOffsets": "361,428,488,550,623,691,748,817,879,940,1013,1081,1142,1196,1259,1324,2047,5213,5279,5345,5415,5486,5564,5643,5706,5770,5838,5907,5982,6058,6120,6183,6257,6332,15253,15326,15401,15474,15540,15593,15656,15722,15779,15854,15932,16007,16082,16142,16202,16262,16328,16393,16453,16525,24326,24414,24500,24587,24647,34661,39861,39930,40001,40076,40160,40242,40307,40360,40505,40641,40781,40923,41057,41200,41340,41424,41486,41558,41624,41690,41754,41816,41894,41972,42026,42078,42130,42198,42268,42331,42399,42473,42529,42593,146739,146911,147311,147486,147699,147913,148014,148115,148208,152321,153820,153993,154162,155768,156296,164389,164673,197288"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\e8868848eae7ccc45186e654e202dd67\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "488", "startColumns": "4", "startOffsets": "31295", "endColumns": "82", "endOffsets": "31373"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\902f73074689cc7f7e9e84c151d54f0b\\transformed\\navigation-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "332,2586,3630,3633", "startColumns": "4,4,4,4", "startOffsets": "21621,168396,204067,204182", "endLines": "332,2592,3632,3635", "endColumns": "52,24,24,24", "endOffsets": "21669,168695,204177,204292"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings_settings.xml", "from": {"startLines": "3,9,5,4,7,6,8,16,15,12,14,13,2,10,11", "startColumns": "8,8,8,8,8,8,8,8,8,8,8,8,4,4,4", "startOffsets": "128,574,272,197,418,337,493,1098,1015,789,931,854,57,655,722", "endColumns": "67,83,63,73,73,79,79,76,81,63,82,75,65,65,61", "endOffsets": "187,649,327,262,483,408,564,1166,1088,844,1005,921,118,716,779"}, "to": {"startLines": "441,442,443,444,445,446,447,448,449,450,451,452,453,454,455", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28109,28173,28253,28313,28383,28453,28529,28605,28678,28756,28816,28895,28967,29033,29099", "endColumns": "63,79,59,69,69,75,75,72,77,59,78,71,65,65,61", "endOffsets": "28168,28248,28308,28378,28448,28524,28600,28673,28751,28811,28890,28962,29028,29094,29156"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "596", "startColumns": "4", "startOffsets": "39219", "endColumns": "57", "endOffsets": "39272"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings_dialogs.xml", "from": {"startLines": "6,5,4,32,43,3,41,15,33,40,9,28,25,42,20,47,24,22,23,50,44,37,36,34,35,53,56,57,16,61,64,60,62,63,29,18,17,10,11,12", "startColumns": "8,8,8,4,4,4,4,4,4,4,4,4,12,8,12,4,12,12,12,4,8,8,8,8,8,4,4,4,8,4,4,4,4,4,8,8,8,8,8,8", "startOffsets": "287,230,174,1559,2222,101,2044,607,1624,1982,374,1406,1295,2116,981,2383,1223,1079,1151,2472,2296,1881,1807,1685,1747,2571,2656,2731,673,2910,3156,2831,3000,3072,1470,894,813,426,475,526", "endColumns": "56,55,54,63,68,67,66,60,55,60,46,58,89,108,68,63,70,70,70,73,66,77,72,60,58,49,73,70,138,88,103,77,70,82,68,57,79,47,49,51", "endOffsets": "335,277,220,1618,2286,164,2106,663,1675,2038,416,1460,1372,2216,1037,2442,1281,1137,1209,2541,2354,1950,1871,1737,1797,2616,2725,2797,803,2994,3255,2904,3066,3150,1530,943,884,465,516,569"}, "to": {"startLines": "404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,456,457,458,459,460", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25574,25627,25679,25730,25794,25863,25931,25998,26059,26115,26176,26223,26282,26364,26469,26530,26594,26657,26720,26783,26857,26920,26994,27063,27120,27175,27225,27299,27370,27505,27594,27698,27776,27847,27930,29161,29215,29291,29335,29381", "endColumns": "52,51,50,63,68,67,66,60,55,60,46,58,81,104,60,63,62,62,62,73,62,73,68,56,54,49,73,70,134,88,103,77,70,82,64,53,75,43,45,47", "endOffsets": "25622,25674,25725,25789,25858,25926,25993,26054,26110,26171,26218,26277,26359,26464,26525,26589,26652,26715,26778,26852,26915,26989,27058,27115,27170,27220,27294,27365,27500,27589,27693,27771,27842,27925,27990,29210,29286,29330,29376,29424"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\adbf5b882905ad8bd23ebd88774c480b\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "327,334", "startColumns": "4,4", "startOffsets": "21369,21717", "endColumns": "53,66", "endOffsets": "21418,21779"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641,3752", "endLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,65,70", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3747,4004"}, "to": {"startLines": "315,318,319,324,326,375,498,499,501,503,504,533,534,638,639,647,648,652,654,656,657,658,661,662,663,1774,1790,1793", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20722,20906,20964,21229,21314,23877,31999,32064,32156,32296,32397,34666,34718,42598,42660,43063,43113,43338,43430,43524,43570,43612,43862,43909,43945,115171,116151,116262", "endLines": "315,318,319,324,326,375,498,499,501,503,504,533,534,638,639,647,648,652,654,656,657,658,661,662,663,1776,1792,1797", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "20791,20959,21014,21275,21364,23925,32059,32113,32217,32392,32450,34713,34773,42655,42709,43108,43162,43379,43479,43565,43607,43647,43904,43940,44030,115278,116257,116514"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3597,3669,3741,3814,3871,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3664,3736,3809,3866,3924,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,31,32,33,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,102,103,104,105,107,108,109,110,111,113,114,115,116,117,118,119,120,121,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,211,212,216,217,218,219,220,221,222,268,269,270,271,272,273,274,275,311,312,313,314,325,338,339,344,368,376,377,379,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,651,669,670,671,672,673,674,682,683,687,691,695,700,706,713,717,721,726,730,734,738,742,746,750,756,760,766,770,776,780,785,789,792,796,802,806,812,816,822,825,829,833,837,841,845,846,847,848,851,854,857,860,864,865,866,867,868,871,873,875,877,882,883,887,893,897,898,900,912,913,917,923,927,928,929,933,960,964,965,969,997,1169,1195,1366,1392,1423,1431,1437,1453,1475,1480,1485,1495,1504,1513,1517,1524,1543,1550,1551,1560,1563,1566,1570,1574,1578,1581,1582,1587,1592,1602,1607,1614,1620,1621,1624,1628,1633,1635,1637,1640,1643,1645,1649,1652,1659,1662,1665,1669,1671,1675,1677,1679,1681,1685,1693,1701,1713,1719,1728,1731,1742,1745,1746,1751,1752,1798,1867,1937,1938,1948,1957,2109,2111,2115,2118,2121,2124,2127,2130,2133,2136,2140,2143,2146,2149,2153,2156,2160,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2186,2188,2189,2190,2191,2192,2193,2194,2195,2197,2198,2200,2201,2203,2205,2206,2208,2209,2210,2211,2212,2213,2215,2216,2217,2218,2219,2262,2264,2266,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2282,2283,2284,2285,2286,2287,2288,2290,2294,2345,2346,2347,2348,2349,2350,2354,2355,2356,2371,2373,2375,2377,2379,2381,2382,2383,2384,2386,2388,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2404,2405,2406,2407,2409,2411,2412,2414,2415,2417,2419,2421,2422,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2434,2435,2436,2437,2439,2440,2441,2442,2443,2445,2447,2449,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2476,2551,2554,2557,2560,2574,2597,2639,2642,2671,2698,2707,2771,3139,3160,3198,3246,3372,3396,3402,3429,3450,3574,3641,3647,3791,3818,3866,3926,4026,4046,4101,4113,4139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,1781,1822,1877,2052,2116,2186,2247,2322,2398,2475,2713,2798,2880,2956,3032,3109,3187,3293,3399,3478,3558,3615,3804,3878,3953,4018,4084,4144,4205,4277,4350,4417,4485,4544,4603,4662,4721,4780,4834,4888,4941,4995,5049,5103,6627,6701,6780,6853,6998,7070,7142,7215,7272,7403,7477,7551,7626,7698,7771,7841,7912,7972,8075,8144,8213,8283,8357,8433,8497,8574,8650,8727,8792,8861,8938,9013,9082,9150,9227,9293,9354,9451,9516,9585,9684,9755,9814,9872,9929,9988,10052,10123,10195,10267,10339,10411,10478,10546,10614,10673,10736,10800,10890,10981,11041,11107,11174,11240,11310,11374,11427,11494,11555,11622,11735,11793,11856,11921,11986,12061,12134,12206,12250,12297,12343,12392,12453,12514,12575,12637,12701,12765,12829,12894,12957,13017,13078,13144,13203,13263,13325,13396,13456,14012,14098,14348,14438,14525,14613,14695,14778,14868,17941,17993,18051,18096,18162,18226,18283,18340,20517,20574,20622,20671,21280,21941,21988,22246,23531,23930,23994,24116,29429,29503,29573,29651,29705,29775,29860,29908,29954,30015,30078,30144,30208,30279,30342,30407,30471,30532,30593,30645,30718,30792,30861,30936,31010,31084,31225,43285,44293,44371,44461,44549,44645,44735,45317,45406,45653,45934,46186,46471,46864,47341,47563,47785,48061,48288,48518,48748,48978,49208,49435,49854,50080,50505,50735,51163,51382,51665,51873,52004,52231,52657,52882,53309,53530,53955,54075,54351,54652,54976,55267,55581,55718,55849,55954,56196,56363,56567,56775,57046,57158,57270,57375,57492,57706,57852,57992,58078,58426,58514,58760,59178,59427,59509,59607,60264,60364,60616,61040,61295,61389,61478,61715,63739,63981,64083,64336,66492,77173,78689,89384,90912,92669,93295,93715,94976,96241,96497,96733,97280,97774,98379,98577,99157,100525,100900,101018,101556,101713,101909,102182,102438,102608,102749,102813,103178,103545,104221,104485,104823,105176,105270,105456,105762,106024,106149,106276,106515,106726,106845,107038,107215,107670,107851,107973,108232,108345,108532,108634,108741,108870,109145,109653,110149,111026,111320,111890,112039,112771,112943,113027,113363,113455,116519,121750,127121,127183,127761,128345,136292,136405,136634,136794,136946,137117,137283,137452,137619,137782,138025,138195,138368,138539,138813,139012,139217,139547,139631,139727,139823,139921,140021,140123,140225,140327,140429,140531,140631,140727,140839,140968,141091,141222,141353,141451,141565,141659,141799,141933,142029,142141,142241,142357,142453,142565,142665,142805,142941,143105,143235,143393,143543,143684,143828,143963,144075,144225,144353,144481,144617,144749,144879,145009,145121,148213,148359,148503,148641,148707,148797,148873,148977,149067,149169,149277,149385,149485,149565,149657,149755,149865,149917,149995,150101,150193,150297,150407,150529,150692,154249,154329,154429,154519,154629,154719,154960,155054,155160,156301,156401,156513,156627,156743,156859,156953,157067,157179,157281,157401,157523,157605,157709,157829,157955,158053,158147,158235,158347,158463,158585,158697,158872,158988,159074,159166,159278,159402,159469,159595,159663,159791,159935,160063,160132,160227,160342,160455,160554,160663,160774,160885,160986,161091,161191,161321,161412,161535,161629,161741,161827,161931,162027,162115,162233,162337,162441,162567,162655,162763,162863,162953,163063,163147,163249,163333,163387,163451,163557,163643,163753,163837,164678,167294,167412,167527,167607,167968,168831,170235,170313,171657,173018,173406,176249,186487,187226,188897,190983,195359,196110,196372,197293,197672,201950,204479,204708,209316,210371,211906,213787,217911,218655,220786,221126,222437", "endLines": "2,3,4,31,32,33,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,102,103,104,105,107,108,109,110,111,113,114,115,116,117,118,119,120,121,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,211,212,216,217,218,219,220,221,222,268,269,270,271,272,273,274,275,311,312,313,314,325,338,339,344,368,376,377,379,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,651,669,670,671,672,673,681,682,686,690,694,699,705,712,716,720,725,729,733,737,741,745,749,755,759,765,769,775,779,784,788,791,795,801,805,811,815,821,824,828,832,836,840,844,845,846,847,850,853,856,859,863,864,865,866,867,870,872,874,876,881,882,886,892,896,897,899,911,912,916,922,926,927,928,932,959,963,964,968,996,1168,1194,1365,1391,1422,1430,1436,1452,1474,1479,1484,1494,1503,1512,1516,1523,1542,1549,1550,1559,1562,1565,1569,1573,1577,1580,1581,1586,1591,1601,1606,1613,1619,1620,1623,1627,1632,1634,1636,1639,1642,1644,1648,1651,1658,1661,1664,1668,1670,1674,1676,1678,1680,1684,1692,1700,1712,1718,1727,1730,1741,1744,1745,1750,1751,1756,1866,1936,1937,1947,1956,1957,2110,2114,2117,2120,2123,2126,2129,2132,2135,2139,2142,2145,2148,2152,2155,2159,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2182,2183,2185,2187,2188,2189,2190,2191,2192,2193,2194,2196,2197,2199,2200,2202,2204,2205,2207,2208,2209,2210,2211,2212,2214,2215,2216,2217,2218,2219,2263,2265,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2281,2282,2283,2284,2285,2286,2287,2289,2293,2297,2345,2346,2347,2348,2349,2353,2354,2355,2356,2372,2374,2376,2378,2380,2381,2382,2383,2385,2387,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2403,2404,2405,2406,2408,2410,2411,2413,2414,2416,2418,2420,2421,2422,2423,2424,2425,2426,2427,2428,2429,2430,2431,2433,2434,2435,2436,2438,2439,2440,2441,2442,2444,2446,2448,2450,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2550,2553,2556,2559,2573,2579,2606,2641,2670,2697,2706,2770,3133,3142,3187,3225,3263,3395,3401,3407,3449,3573,3593,3646,3650,3796,3852,3877,3991,4045,4100,4112,4138,4145", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,1817,1872,1934,2111,2181,2242,2317,2393,2470,2548,2793,2875,2951,3027,3104,3182,3288,3394,3473,3553,3610,3668,3873,3948,4013,4079,4139,4200,4272,4345,4412,4480,4539,4598,4657,4716,4775,4829,4883,4936,4990,5044,5098,5152,6696,6775,6848,6922,7065,7137,7210,7267,7325,7472,7546,7621,7693,7766,7836,7907,7967,8028,8139,8208,8278,8352,8428,8492,8569,8645,8722,8787,8856,8933,9008,9077,9145,9222,9288,9349,9446,9511,9580,9679,9750,9809,9867,9924,9983,10047,10118,10190,10262,10334,10406,10473,10541,10609,10668,10731,10795,10885,10976,11036,11102,11169,11235,11305,11369,11422,11489,11550,11617,11730,11788,11851,11916,11981,12056,12129,12201,12245,12292,12338,12387,12448,12509,12570,12632,12696,12760,12824,12889,12952,13012,13073,13139,13198,13258,13320,13391,13451,13519,14093,14180,14433,14520,14608,14690,14773,14863,14954,17988,18046,18091,18157,18221,18278,18335,18389,20569,20617,20666,20717,21309,21983,22032,22287,23558,23989,24051,24168,29498,29568,29646,29700,29770,29855,29903,29949,30010,30073,30139,30203,30274,30337,30402,30466,30527,30588,30640,30713,30787,30856,30931,31005,31079,31220,31290,43333,44366,44456,44544,44640,44730,45312,45401,45648,45929,46181,46466,46859,47336,47558,47780,48056,48283,48513,48743,48973,49203,49430,49849,50075,50500,50730,51158,51377,51660,51868,51999,52226,52652,52877,53304,53525,53950,54070,54346,54647,54971,55262,55576,55713,55844,55949,56191,56358,56562,56770,57041,57153,57265,57370,57487,57701,57847,57987,58073,58421,58509,58755,59173,59422,59504,59602,60259,60359,60611,61035,61290,61384,61473,61710,63734,63976,64078,64331,66487,77168,78684,89379,90907,92664,93290,93710,94971,96236,96492,96728,97275,97769,98374,98572,99152,100520,100895,101013,101551,101708,101904,102177,102433,102603,102744,102808,103173,103540,104216,104480,104818,105171,105265,105451,105757,106019,106144,106271,106510,106721,106840,107033,107210,107665,107846,107968,108227,108340,108527,108629,108736,108865,109140,109648,110144,111021,111315,111885,112034,112766,112938,113022,113358,113450,113728,121745,127116,127178,127756,128340,128431,136400,136629,136789,136941,137112,137278,137447,137614,137777,138020,138190,138363,138534,138808,139007,139212,139542,139626,139722,139818,139916,140016,140118,140220,140322,140424,140526,140626,140722,140834,140963,141086,141217,141348,141446,141560,141654,141794,141928,142024,142136,142236,142352,142448,142560,142660,142800,142936,143100,143230,143388,143538,143679,143823,143958,144070,144220,144348,144476,144612,144744,144874,145004,145116,145256,148354,148498,148636,148702,148792,148868,148972,149062,149164,149272,149380,149480,149560,149652,149750,149860,149912,149990,150096,150188,150292,150402,150524,150687,150844,154324,154424,154514,154624,154714,154955,155049,155155,155247,156396,156508,156622,156738,156854,156948,157062,157174,157276,157396,157518,157600,157704,157824,157950,158048,158142,158230,158342,158458,158580,158692,158867,158983,159069,159161,159273,159397,159464,159590,159658,159786,159930,160058,160127,160222,160337,160450,160549,160658,160769,160880,160981,161086,161186,161316,161407,161530,161624,161736,161822,161926,162022,162110,162228,162332,162436,162562,162650,162758,162858,162948,163058,163142,163244,163328,163382,163446,163552,163638,163748,163832,163952,167289,167407,167522,167602,167963,168196,169343,170308,171652,173013,173401,176244,186297,186617,188591,190249,191550,196105,196367,196567,197667,201945,202551,204703,204854,209526,211449,212213,216808,218650,220781,221121,222432,222635"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\strings_actions.xml", "from": {"startLines": "13,21,3,23,18,16,15,9,4,5,7,10,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "409,677,78,746,605,553,505,276,124,174,224,322,453", "endColumns": "42,65,44,52,44,48,46,44,48,46,48,50,50", "endOffsets": "447,738,118,794,645,597,547,316,168,216,268,368,499"}, "to": {"startLines": "389,390,391,392,393,394,395,396,397,398,399,400,401", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24848,24891,24957,25002,25055,25100,25149,25196,25241,25290,25337,25386,25437", "endColumns": "42,65,44,52,44,48,46,44,48,46,48,50,50", "endOffsets": "24886,24952,24997,25050,25095,25144,25191,25236,25285,25332,25381,25432,25483"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4ec837fe980ac62f82a7402163f3b9e1\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "323", "startColumns": "4", "startOffsets": "21163", "endColumns": "65", "endOffsets": "21224"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "34,101,261,262,263,264,265,266,267,335,336,337,386,387,500,531,640,642,660,666,667,1757,1958,1961,1967,1973,1976,1982,1986,1989,1996,2002,2005,2011,2016,2021,2028,2030,2036,2042,2050,2055,2062,2067,2073,2077,2084,2088,2094,2100,2103,2107,2108,3134,3149,3226,3264,3408,3594,3651,3715,3725,3735,3742,3748,3853,3992,4009", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1939,6558,17508,17572,17627,17695,17762,17827,17884,21784,21832,21880,24652,24715,32118,34523,42714,42806,43723,44146,44196,113733,128436,128541,128786,129124,129270,129610,129822,129985,130392,130730,130853,131192,131431,131688,132059,132119,132457,132743,133192,133484,133872,134177,134521,134766,135096,135303,135571,135844,135988,136189,136236,186302,186825,190254,191555,196572,202556,204859,206784,207066,207371,207633,207893,211454,216813,217343", "endLines": "34,101,261,262,263,264,265,266,267,335,336,337,386,387,500,531,640,644,660,666,667,1773,1960,1966,1972,1975,1981,1985,1988,1995,2001,2004,2010,2015,2020,2027,2029,2035,2041,2049,2054,2061,2066,2072,2076,2083,2087,2093,2099,2102,2106,2107,2108,3138,3159,3245,3267,3417,3601,3714,3724,3734,3741,3747,3790,3865,4008,4025", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2007,6622,17567,17622,17690,17757,17822,17879,17936,21827,21875,21936,24710,24773,32151,34575,42753,42941,43857,44191,44239,115166,128536,128781,129119,129265,129605,129817,129980,130387,130725,130848,131187,131426,131683,132054,132114,132452,132738,133187,133479,133867,134172,134516,134761,135091,135298,135566,135839,135983,136184,136231,136287,186482,187221,190978,191699,196899,202799,206779,207061,207366,207628,207888,209311,211901,217338,217906"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\cc1d3805fe8d4fc761fa3c8cb2574116\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "321,322,333,341,342,363,364,365,366,367", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "21076,21116,21674,22079,22134,23265,23319,23371,23420,23481", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "21111,21158,21712,22129,22176,23314,23366,23415,23476,23526"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\89628933edb8d4035434c63d83660fcd\\transformed\\navigation-common-release\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3602,3615,3621,3627,3636", "startColumns": "4,4,4,4,4", "startOffsets": "202804,203443,203687,203934,204297", "endLines": "3614,3620,3626,3629,3640", "endColumns": "24,24,24,24,24", "endOffsets": "203438,203682,203929,204062,204474"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\6a19f80353fbc6fa60e88fd607d06113\\transformed\\recyclerview-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,38", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,2084"}, "to": {"startLines": "22,213,214,215,223,224,225,328,3797", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1389,14185,14244,14292,14959,15034,15110,21423,209531", "endLines": "22,213,214,215,223,224,225,328,3817", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "1440,14239,14287,14343,15029,15105,15177,21484,210366"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4ff8829df674d3dbd062b8350784bf54\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "320,343,374,3324,3329", "startColumns": "4,4,4,4,4", "startOffsets": "21019,22181,23813,194191,194361", "endLines": "320,343,374,3328,3332", "endColumns": "56,64,63,24,24", "endOffsets": "21071,22241,23872,194356,194505"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "82", "endOffsets": "135"}, "to": {"startLines": "2344", "startColumns": "4", "startOffsets": "154167", "endColumns": "81", "endOffsets": "154244"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\6cddd03f981b66f6053ec06c3046d7ce\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "369", "startColumns": "4", "startOffsets": "23563", "endColumns": "42", "endOffsets": "23601"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "41", "endOffsets": "94"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "8033", "endColumns": "41", "endOffsets": "8070"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\3d2aea75d7f968093da044482f8492b7\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2607,2623,2629,3906,3922", "startColumns": "4,4,4,4,4", "startOffsets": "169348,169773,169951,213249,213660", "endLines": "2622,2628,2638,3921,3925", "endColumns": "24,24,24,24,24", "endOffsets": "169768,169946,170230,213655,213782"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c7890e83d7f221a44550e26073b9aa79\\transformed\\coil-base-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "317", "startColumns": "4", "startOffsets": "20856", "endColumns": "49", "endOffsets": "20901"}}]}]}