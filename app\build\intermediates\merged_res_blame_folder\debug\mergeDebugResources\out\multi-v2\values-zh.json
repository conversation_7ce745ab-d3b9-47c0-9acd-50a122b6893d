{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-zh/values-zh.xml", "map": [{"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,11", "startColumns": "-1,-1,-1,-1,-1,-1,-1,4", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,363", "endColumns": "-1,-1,-1,-1,-1,-1,-1,61", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,420"}, "to": {"startLines": "19,20,95,97,98,102,103,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "824,862,5099,5208,5251,5439,5479,5560", "endColumns": "37,39,63,42,39,39,36,61", "endOffsets": "857,897,5158,5246,5286,5474,5511,5617"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values-zh\\strings_dialogs.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "3,4,5,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "97,140,185,685,902,949,996,1044,1096,1155,1211,1269,1333,1385,1437,1492,1548,1592,1642,1694,1760,1813,1863,1935,1992,2046,2101,2158,2220,2278,2332,2386,2442,2513,2570,2621,2671,2726,2779,2826,2891,2951,3025,3087,3157,3217,3280,3344,4845,4892,4968,5010,5053", "endColumns": "42,44,45,44,46,46,47,51,58,55,57,63,51,51,54,55,43,49,51,65,52,49,71,56,53,54,56,61,57,53,53,55,70,56,50,49,54,52,46,64,59,73,61,69,59,62,63,53,46,75,41,42,45", "endOffsets": "135,180,226,725,944,991,1039,1091,1150,1206,1264,1328,1380,1432,1487,1543,1587,1637,1689,1755,1808,1858,1930,1987,2041,2096,2153,2215,2273,2327,2381,2437,2508,2565,2616,2666,2721,2774,2821,2886,2946,3020,3082,3152,3212,3275,3339,3393,4887,4963,5005,5048,5094"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values-zh\\strings_actions.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,231,288,331,378,421,464,508,551,596,640,730,778", "endColumns": "41,56,42,46,42,42,43,42,44,43,44,47,45", "endOffsets": "92,283,326,373,416,459,503,546,591,635,680,773,819"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values-zh\\strings_screens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "65,66,96,99,100,101,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3398,3449,5163,5291,5342,5390,5516", "endColumns": "50,49,44,50,47,48,43", "endOffsets": "3444,3494,5203,5337,5385,5434,5555"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values-zh\\strings_settings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3499,3552,3602,3654,3707,3761,3813,3878,3942,3998,4050,4105,4171,4235,4297,4368,4429,4483,4549,4615,4675,4733,4789", "endColumns": "52,49,51,52,53,51,64,63,55,51,54,65,63,61,70,60,53,65,65,59,57,55,55", "endOffsets": "3547,3597,3649,3702,3756,3808,3873,3937,3993,4045,4100,4166,4230,4292,4363,4424,4478,4544,4610,4670,4728,4784,4840"}}]}, {"outputFile": "E:\\android_development_cache\\.gradle\\daemon\\8.11.1\\com.craftworks.music.app-mergeDebugResources-3:\\values-zh\\values-zh.xml", "map": [{"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "4,5,1,8,7,6,3", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "141,179,16,299,259,219,104", "endColumns": "37,39,63,42,39,39,36", "endOffsets": "174,214,75,337,294,254,136"}, "to": {"startLines": "19,20,95,97,98,102,103", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "824,862,5099,5208,5251,5439,5479", "endColumns": "37,39,63,42,39,39,36", "endOffsets": "857,897,5158,5246,5286,5474,5511"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values-zh\\strings_dialogs.xml", "from": {"startLines": "79,76,77,78,6,5,4,32,43,3,41,68,15,33,40,67,9,28,71,25,74,75,42,69,20,47,24,22,23,50,73,44,37,36,34,35,72,70,53,56,57,16,61,64,60,62,63,29,18,17,10,11,12", "startColumns": "4,4,4,4,8,8,8,4,4,4,4,4,4,4,4,4,4,4,4,12,4,4,8,4,12,4,12,12,12,4,4,8,8,8,8,8,4,4,4,4,4,8,4,4,4,4,4,8,8,8,8,8,8", "startOffsets": "3438,3302,3347,3393,261,210,158,1359,1921,98,1787,2864,556,1411,1732,2808,339,1230,3038,1138,3199,3252,1849,2928,848,2061,1073,937,1007,2138,3145,1984,1637,1576,1467,1522,3090,2985,2214,2293,2358,612,2504,2693,2444,2566,2629,1284,770,690,387,433,480", "endColumns": "42,44,45,44,50,50,51,51,58,55,57,63,51,51,54,55,43,49,51,73,52,49,75,56,61,54,64,69,65,53,53,59,74,60,54,53,54,52,46,64,59,77,61,69,59,62,63,57,50,79,45,46,49", "endOffsets": "3476,3342,3388,3433,303,252,201,1406,1975,149,1840,2923,603,1458,1782,2859,378,1275,3085,1199,3247,3297,1916,2980,897,2111,1125,994,1060,2187,3194,2035,1703,1628,1513,1567,3140,3033,2256,2353,2413,681,2561,2758,2499,2624,2688,1333,812,761,424,471,521"}, "to": {"startLines": "3,4,5,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "97,140,185,685,902,949,996,1044,1096,1155,1211,1269,1333,1385,1437,1492,1548,1592,1642,1694,1760,1813,1863,1935,1992,2046,2101,2158,2220,2278,2332,2386,2442,2513,2570,2621,2671,2726,2779,2826,2891,2951,3025,3087,3157,3217,3280,3344,4845,4892,4968,5010,5053", "endColumns": "42,44,45,44,46,46,47,51,58,55,57,63,51,51,54,55,43,49,51,65,52,49,71,56,53,54,56,61,57,53,53,55,70,56,50,49,54,52,46,64,59,73,61,69,59,62,63,53,46,75,41,42,45", "endOffsets": "135,180,226,725,944,991,1039,1091,1150,1206,1264,1328,1380,1432,1487,1543,1587,1637,1689,1755,1808,1858,1930,1987,2041,2096,2153,2215,2273,2327,2381,2437,2508,2565,2616,2666,2721,2774,2821,2886,2946,3020,3082,3152,3212,3275,3339,3393,4887,4963,5005,5048,5094"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values-zh\\strings_actions.xml", "from": {"startLines": "13,21,3,23,18,16,15,9,4,5,7,10,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "378,621,75,679,554,510,466,254,118,163,208,297,420", "endColumns": "41,56,42,46,42,42,43,42,44,43,44,47,45", "endOffsets": "415,673,113,721,592,548,505,292,158,202,248,340,461"}, "to": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,231,288,331,378,421,464,508,551,596,640,730,778", "endColumns": "41,56,42,46,42,42,43,42,44,43,44,47,45", "endOffsets": "92,283,326,373,416,459,503,546,591,635,680,773,819"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values-zh\\strings_screens.xml", "from": {"startLines": "10,11,5,7,4,6,3", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "339,390,165,259,117,210,73", "endColumns": "50,49,44,50,47,48,43", "endOffsets": "385,435,205,305,160,254,112"}, "to": {"startLines": "65,66,96,99,100,101,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3398,3449,5163,5291,5342,5390,5516", "endColumns": "50,49,44,50,47,48,43", "endOffsets": "3444,3494,5203,5337,5385,5434,5555"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values-zh\\strings_settings.xml", "from": {"startLines": "19,21,3,18,24,23,20,9,22,5,4,7,6,8,16,15,12,14,13,17,2,10,11", "startColumns": "8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,4,4,4", "startOffsets": "1131,1257,117,1074,1427,1371,1188,492,1311,232,173,356,288,426,935,870,672,800,730,1010,55,556,612", "endColumns": "56,53,55,56,57,55,68,67,59,55,58,69,67,65,74,64,57,69,69,63,57,55,55", "endOffsets": "1179,1302,164,1122,1476,1418,1248,551,1362,279,223,417,347,483,1001,926,721,861,791,1065,108,607,663"}, "to": {"startLines": "67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3499,3552,3602,3654,3707,3761,3813,3878,3942,3998,4050,4105,4171,4235,4297,4368,4429,4483,4549,4615,4675,4733,4789", "endColumns": "52,49,51,52,53,51,64,63,55,51,54,65,63,61,70,60,53,65,65,59,57,55,55", "endOffsets": "3547,3597,3649,3702,3756,3808,3873,3937,3993,4045,4100,4166,4230,4292,4363,4424,4478,4544,4610,4670,4728,4784,4840"}}]}]}