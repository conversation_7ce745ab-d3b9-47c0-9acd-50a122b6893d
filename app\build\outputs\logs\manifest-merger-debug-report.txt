-- Merging decision tree log ---
manifest
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:2:1-82:12
INJECTED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:2:1-82:12
INJECTED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:2:1-82:12
INJECTED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:2:1-82:12
MERGED from [androidx.media3:media3-exoplayer:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\114a5d5301d7649ab21683cbabeecc89\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-session:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\493af54b1b0004569d1870cf1ba0af14\transformed\media3-session-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\5e60f2f32bf8007354b1cf26f878b14a\transformed\media3-extractor-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\b536a3479597b8571f11eb95e91db7c3\transformed\media3-container-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\1a227d1d2e5bb9dea5b2da6117e28cfe\transformed\media3-datasource-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\498213b301fea9efdcced697ea8aa15b\transformed\media3-decoder-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\dfb68a4493b701c53f39603b5b60dbd2\transformed\media3-database-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common-ktx:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\608f11a0b27f0a3f58b8ef7f01ab7810\transformed\media3-common-ktx-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\366408bcdded224ef4b677da8c9e5d22\transformed\media3-common-1.7.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui-compose:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\5b31017e82d3350b0b101a68d2a9a97a\transformed\media3-ui-compose-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.GIGAMOLE:ComposeFadingEdges:1.0.4] E:\android_development_cache\.gradle\caches\8.11.1\transforms\3b3a802afe5ecb9eb3a516ecd6328ca6\transformed\ComposeFadingEdges-1.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] E:\android_development_cache\.gradle\caches\8.11.1\transforms\c9938815eb16e1bbe389a6512658438a\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.preference:preference-ktx:1.2.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\47fdc7a6d031f05dc72ed502ffca8da4\transformed\preference-ktx-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\4f725fd807947c49350c5a696b32f0e9\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.3.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\8cbdec45e8f1d8e2169eac561adc8f8d\transformed\fragment-ktx-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\902f73074689cc7f7e9e84c151d54f0b\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\89628933edb8d4035434c63d83660fcd\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\baf53a0d383871b16346bc83cfe5e047\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose:2.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\2b02baad47d75acba8b7efb1d0f708f6\transformed\coil-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\cd78be55cca365a7159e089732c91d2d\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\144bd5956e6073a214ec14e3edad832a\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [sh.calvin.reorderable:reorderable-android-debug:2.4.3] E:\android_development_cache\.gradle\caches\8.11.1\transforms\08d42c989e51ba83565daa5e6fbeab76\transformed\reorderable-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\c3709e4bfd5d4e5c1ed8a624d490cd12\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e90c12ecf9c6e190c41b310c54128007\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\83b48cd9789d2fe03dfe7c9497ad2957\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\b76f442cbb3906dc341409c04ee5b003\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\bc3d2f58eeeddd08d95d005ece117cc3\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\886fef481c8ce4a2616ccf22a8cd1328\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\d0f75e3a8d52cb6fac20a81127e53140\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\8dcafffe1dbd2913da26936244d49e40\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\87e5b8e840b827a132ec3241bc5f6088\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\ef34449bb5dff65d7c2e3d914139459f\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\f09740c494ceadcd245bcae1943ae9f6\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\5cc9bbee9ebde3a4190ed469ad1f8e1a\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\4ec837fe980ac62f82a7402163f3b9e1\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] E:\android_development_cache\.gradle\caches\8.11.1\transforms\36a42967e4b2d03de9c6b5477c04706e\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.mediarouter:mediarouter:1.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\2fbbcb27eaccaf382f754b7d51fec492\transformed\mediarouter-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\248e89ea72a9fdbc7978876cda3c2030\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.palette:palette-ktx:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33ec6e4d01f5317bef6f23f08485ba81\transformed\palette-ktx-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.palette:palette:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\3f07e98afc333cb6f35938d67bd4f92d\transformed\palette-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\6a19f80353fbc6fa60e88fd607d06113\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\d89a43babbc69533de57aa485d8f3009\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [io.coil-kt:coil:2.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\41011880b9499f204d9cc3d1118c738b\transformed\coil-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\c7890e83d7f221a44550e26073b9aa79\transformed\coil-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\3d2aea75d7f968093da044482f8492b7\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\64a01e76fd68fe16459fb0e17d50396b\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.3.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\4ff8829df674d3dbd062b8350784bf54\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\97c0011b0281a718aa8cc2f30cd219c6\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a11632626e3230adc14a529da713cb95\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\9c47a2470d36934db743cfbc211f35f2\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\d1205517d434e2f23b038317e41b85b7\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.transition:transition:1.4.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\cc1d3805fe8d4fc761fa3c8cb2574116\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\380d203cece9f4a2ad5406cc28fe6a41\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\efc564e67b5f5811a5425c45114fb084\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\b93506ce39dd89b36b2788f773d47a71\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\8c85d6b0476ffc76fb7650ad64b5392c\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\32eaec6d1e556a4fe0e9578e3cce695e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\457bba70bddf97b78711050d0d296cb4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\77e920b03d0b087c51ffbd4c105a44b5\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\79985d61dce9044eb78b114a336bf145\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\465b20d6778807fef3dfa0b779704fe6\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\26cfaaab73dd904d54baabb804da8de1\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\4106c3afa0ab663148d90fcfcf1acc2c\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\754763af6e012e597e57d1d6e7d6d983\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\ee0c7c81f50beedd6a9b98d0da0f7274\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\bd6d54c7e3fa45aaa4b45bb0ab3080e6\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\2ef59a3900bc6a0162f77e6c6369129b\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\ae0358a2c93f2661aabc6c05923ecb92\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\cac43e177d38c96eb682e0b63c61a4fe\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\5d2c8e8fd1e35d7a1f7cc76443dff543\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a2597c41e05f7165bc731d5973035545\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\411fc5cafa49190fecc481318874d364\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\b12f5ec7860c79fe319ea01d980e4aac\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.activity:activity:1.10.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\7109a23afcec01fecc6451570c536a6e\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\7ee2f5ae46b8b5ca8aca69086d262583\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\7ded0d3e29af577fc8ec3b647a40ef07\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\adbf5b882905ad8bd23ebd88774c480b\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\7ed66526869fe9508195749b4a76521f\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-core-android:1.1.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\c3d60fc26ee2d9aa37847d5633efcdbb\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\f8d9f7d29ffa753ad60497c889a22b79\transformed\datastore-preferences-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\9890182d3e55a8b00f8ae4ebdd8fcd3c\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\cde17cea5b6708a047d75e9329bc15c9\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\19126d6c5cae32eced731ac52afac268\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a9868b39cc567603bcdb39fa45db6dee\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\ffd9c8c98b99132324bc77dd786b1cb7\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\6cddd03f981b66f6053ec06c3046d7ce\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\8e203b00f181f9efd9b72b0103240e53\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\efeec16633ba2aa47bdc0240bc5612c4\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e8868848eae7ccc45186e654e202dd67\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\c025c2a7d9b0b6d0c82fa5b4b20e98fa\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] E:\android_development_cache\.gradle\caches\8.11.1\transforms\d0570d073c0340282798e2e43d0389b3\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\30db5274f68c630462674577ed3695b2\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\60bd08f5c0cca82d92c934f0df393e13\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\ed47751eec0b5bd9b7ecc2db0c215400\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\51dc201c90802576836ef22b82cc97d5\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\440420c1464a580f64d615cb46f54ca1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:6:5-77
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:6:22-74
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:7:5-75
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:7:22-72
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:8:5-109
	tools:ignore
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:8:78-106
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:8:22-77
uses-permission#android.permission.WAKE_LOCK
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:9:5-68
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:9:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:12:5-77
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:12:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:13:5-92
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:13:22-89
uses-feature#android.hardware.wifi
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:15:5-17:36
	android:required
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:17:9-33
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:16:9-45
uses-feature#android.software.leanback
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:18:5-20:36
	android:required
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:20:9-33
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:19:9-49
uses-feature#android.hardware.touchscreen
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:21:5-23:36
	android:required
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:23:9-33
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:22:9-52
application
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:25:5-81:19
INJECTED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:25:5-81:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\8e203b00f181f9efd9b72b0103240e53\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\8e203b00f181f9efd9b72b0103240e53\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e8868848eae7ccc45186e654e202dd67\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e8868848eae7ccc45186e654e202dd67\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml
	android:appCategory
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:36:9-36
	tools:ignore
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:40:9-39
	android:roundIcon
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:32:9-48
	android:icon
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:30:9-43
	android:banner
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:27:9-43
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:33:9-35
	android:label
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:31:9-41
	android:fullBackupContent
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:29:9-54
	tools:targetApi
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:39:9-29
	android:allowBackup
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:26:9-35
	android:theme
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:34:9-49
	android:enableOnBackInvokedCallback
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:37:9-51
	android:dataExtractionRules
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:28:9-65
	android:usesCleartextTraffic
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:35:9-44
meta-data#com.google.android.gms.car.application
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:42:9-43:58
	android:resource
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:43:13-56
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:42:20-73
activity#com.craftworks.music.MainActivity
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:45:9-60:20
	android:launchMode
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:47:13-48
	android:hardwareAccelerated
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:50:13-47
	android:exported
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:48:13-36
	tools:ignore
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:54:13-53
	android:configChanges
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:52:13-59
	android:theme
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:49:13-53
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:46:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:55:13-59:29
action#android.intent.action.MAIN
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:56:17-69
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:56:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:57:17-77
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:57:27-74
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:58:17-86
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:58:27-83
service#com.craftworks.music.player.ChoraMediaLibraryService
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:63:9-71:19
	android:exported
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:66:13-36
	android:permission
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:67:13-71
	android:foregroundServiceType
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:65:13-58
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:64:13-60
intent-filter#action:name:android.media.browse.MediaBrowserService
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:68:13-70:29
action#android.media.browse.MediaBrowserService
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:69:17-82
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:69:25-80
receiver#androidx.media3.session.MediaButtonReceiver
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:74:9-79:20
	android:exported
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:75:13-36
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:74:19-77
intent-filter#action:name:android.intent.action.MEDIA_BUTTON
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:76:13-78:29
action#android.intent.action.MEDIA_BUTTON
ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:77:17-77
	android:name
		ADDED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml:77:25-74
uses-sdk
INJECTED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml
INJECTED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml
MERGED from [androidx.media3:media3-exoplayer:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\114a5d5301d7649ab21683cbabeecc89\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\114a5d5301d7649ab21683cbabeecc89\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\493af54b1b0004569d1870cf1ba0af14\transformed\media3-session-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\493af54b1b0004569d1870cf1ba0af14\transformed\media3-session-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\5e60f2f32bf8007354b1cf26f878b14a\transformed\media3-extractor-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\5e60f2f32bf8007354b1cf26f878b14a\transformed\media3-extractor-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\b536a3479597b8571f11eb95e91db7c3\transformed\media3-container-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\b536a3479597b8571f11eb95e91db7c3\transformed\media3-container-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\1a227d1d2e5bb9dea5b2da6117e28cfe\transformed\media3-datasource-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\1a227d1d2e5bb9dea5b2da6117e28cfe\transformed\media3-datasource-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\498213b301fea9efdcced697ea8aa15b\transformed\media3-decoder-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\498213b301fea9efdcced697ea8aa15b\transformed\media3-decoder-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\dfb68a4493b701c53f39603b5b60dbd2\transformed\media3-database-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\dfb68a4493b701c53f39603b5b60dbd2\transformed\media3-database-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common-ktx:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\608f11a0b27f0a3f58b8ef7f01ab7810\transformed\media3-common-ktx-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common-ktx:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\608f11a0b27f0a3f58b8ef7f01ab7810\transformed\media3-common-ktx-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\366408bcdded224ef4b677da8c9e5d22\transformed\media3-common-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\366408bcdded224ef4b677da8c9e5d22\transformed\media3-common-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui-compose:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\5b31017e82d3350b0b101a68d2a9a97a\transformed\media3-ui-compose-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui-compose:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\5b31017e82d3350b0b101a68d2a9a97a\transformed\media3-ui-compose-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.GIGAMOLE:ComposeFadingEdges:1.0.4] E:\android_development_cache\.gradle\caches\8.11.1\transforms\3b3a802afe5ecb9eb3a516ecd6328ca6\transformed\ComposeFadingEdges-1.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.github.GIGAMOLE:ComposeFadingEdges:1.0.4] E:\android_development_cache\.gradle\caches\8.11.1\transforms\3b3a802afe5ecb9eb3a516ecd6328ca6\transformed\ComposeFadingEdges-1.0.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] E:\android_development_cache\.gradle\caches\8.11.1\transforms\c9938815eb16e1bbe389a6512658438a\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] E:\android_development_cache\.gradle\caches\8.11.1\transforms\c9938815eb16e1bbe389a6512658438a\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.preference:preference-ktx:1.2.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\47fdc7a6d031f05dc72ed502ffca8da4\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference-ktx:1.2.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\47fdc7a6d031f05dc72ed502ffca8da4\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\4f725fd807947c49350c5a696b32f0e9\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\4f725fd807947c49350c5a696b32f0e9\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.3.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\8cbdec45e8f1d8e2169eac561adc8f8d\transformed\fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.3.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\8cbdec45e8f1d8e2169eac561adc8f8d\transformed\fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\902f73074689cc7f7e9e84c151d54f0b\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\902f73074689cc7f7e9e84c151d54f0b\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\89628933edb8d4035434c63d83660fcd\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\89628933edb8d4035434c63d83660fcd\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\baf53a0d383871b16346bc83cfe5e047\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\baf53a0d383871b16346bc83cfe5e047\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\2b02baad47d75acba8b7efb1d0f708f6\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\2b02baad47d75acba8b7efb1d0f708f6\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\cd78be55cca365a7159e089732c91d2d\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\cd78be55cca365a7159e089732c91d2d\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\144bd5956e6073a214ec14e3edad832a\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\144bd5956e6073a214ec14e3edad832a\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [sh.calvin.reorderable:reorderable-android-debug:2.4.3] E:\android_development_cache\.gradle\caches\8.11.1\transforms\08d42c989e51ba83565daa5e6fbeab76\transformed\reorderable-debug\AndroidManifest.xml:5:5-44
MERGED from [sh.calvin.reorderable:reorderable-android-debug:2.4.3] E:\android_development_cache\.gradle\caches\8.11.1\transforms\08d42c989e51ba83565daa5e6fbeab76\transformed\reorderable-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\c3709e4bfd5d4e5c1ed8a624d490cd12\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\c3709e4bfd5d4e5c1ed8a624d490cd12\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e90c12ecf9c6e190c41b310c54128007\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e90c12ecf9c6e190c41b310c54128007\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\83b48cd9789d2fe03dfe7c9497ad2957\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\83b48cd9789d2fe03dfe7c9497ad2957\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\b76f442cbb3906dc341409c04ee5b003\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\b76f442cbb3906dc341409c04ee5b003\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\bc3d2f58eeeddd08d95d005ece117cc3\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\bc3d2f58eeeddd08d95d005ece117cc3\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\886fef481c8ce4a2616ccf22a8cd1328\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\886fef481c8ce4a2616ccf22a8cd1328\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\d0f75e3a8d52cb6fac20a81127e53140\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\d0f75e3a8d52cb6fac20a81127e53140\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\8dcafffe1dbd2913da26936244d49e40\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\8dcafffe1dbd2913da26936244d49e40\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\87e5b8e840b827a132ec3241bc5f6088\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\87e5b8e840b827a132ec3241bc5f6088\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\ef34449bb5dff65d7c2e3d914139459f\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\ef34449bb5dff65d7c2e3d914139459f\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\f09740c494ceadcd245bcae1943ae9f6\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\f09740c494ceadcd245bcae1943ae9f6\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\5cc9bbee9ebde3a4190ed469ad1f8e1a\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\5cc9bbee9ebde3a4190ed469ad1f8e1a\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\4ec837fe980ac62f82a7402163f3b9e1\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\4ec837fe980ac62f82a7402163f3b9e1\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] E:\android_development_cache\.gradle\caches\8.11.1\transforms\36a42967e4b2d03de9c6b5477c04706e\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] E:\android_development_cache\.gradle\caches\8.11.1\transforms\36a42967e4b2d03de9c6b5477c04706e\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.mediarouter:mediarouter:1.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\2fbbcb27eaccaf382f754b7d51fec492\transformed\mediarouter-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.mediarouter:mediarouter:1.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\2fbbcb27eaccaf382f754b7d51fec492\transformed\mediarouter-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\248e89ea72a9fdbc7978876cda3c2030\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\248e89ea72a9fdbc7978876cda3c2030\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.palette:palette-ktx:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33ec6e4d01f5317bef6f23f08485ba81\transformed\palette-ktx-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.palette:palette-ktx:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33ec6e4d01f5317bef6f23f08485ba81\transformed\palette-ktx-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.palette:palette:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\3f07e98afc333cb6f35938d67bd4f92d\transformed\palette-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.palette:palette:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\3f07e98afc333cb6f35938d67bd4f92d\transformed\palette-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\6a19f80353fbc6fa60e88fd607d06113\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\6a19f80353fbc6fa60e88fd607d06113\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\d89a43babbc69533de57aa485d8f3009\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\d89a43babbc69533de57aa485d8f3009\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [io.coil-kt:coil:2.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\41011880b9499f204d9cc3d1118c738b\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\41011880b9499f204d9cc3d1118c738b\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\c7890e83d7f221a44550e26073b9aa79\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\c7890e83d7f221a44550e26073b9aa79\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\3d2aea75d7f968093da044482f8492b7\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\3d2aea75d7f968093da044482f8492b7\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\64a01e76fd68fe16459fb0e17d50396b\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\64a01e76fd68fe16459fb0e17d50396b\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.3.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\4ff8829df674d3dbd062b8350784bf54\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\4ff8829df674d3dbd062b8350784bf54\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\97c0011b0281a718aa8cc2f30cd219c6\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\97c0011b0281a718aa8cc2f30cd219c6\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a11632626e3230adc14a529da713cb95\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a11632626e3230adc14a529da713cb95\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\9c47a2470d36934db743cfbc211f35f2\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\9c47a2470d36934db743cfbc211f35f2\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\d1205517d434e2f23b038317e41b85b7\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\d1205517d434e2f23b038317e41b85b7\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\cc1d3805fe8d4fc761fa3c8cb2574116\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\cc1d3805fe8d4fc761fa3c8cb2574116\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\380d203cece9f4a2ad5406cc28fe6a41\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\380d203cece9f4a2ad5406cc28fe6a41\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\efc564e67b5f5811a5425c45114fb084\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\efc564e67b5f5811a5425c45114fb084\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\b93506ce39dd89b36b2788f773d47a71\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\b93506ce39dd89b36b2788f773d47a71\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\8c85d6b0476ffc76fb7650ad64b5392c\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\8c85d6b0476ffc76fb7650ad64b5392c\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\32eaec6d1e556a4fe0e9578e3cce695e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\32eaec6d1e556a4fe0e9578e3cce695e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\457bba70bddf97b78711050d0d296cb4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\457bba70bddf97b78711050d0d296cb4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\77e920b03d0b087c51ffbd4c105a44b5\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\77e920b03d0b087c51ffbd4c105a44b5\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\79985d61dce9044eb78b114a336bf145\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\79985d61dce9044eb78b114a336bf145\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\465b20d6778807fef3dfa0b779704fe6\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\465b20d6778807fef3dfa0b779704fe6\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\26cfaaab73dd904d54baabb804da8de1\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\26cfaaab73dd904d54baabb804da8de1\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\4106c3afa0ab663148d90fcfcf1acc2c\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\4106c3afa0ab663148d90fcfcf1acc2c\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\754763af6e012e597e57d1d6e7d6d983\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\754763af6e012e597e57d1d6e7d6d983\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\ee0c7c81f50beedd6a9b98d0da0f7274\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\ee0c7c81f50beedd6a9b98d0da0f7274\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\bd6d54c7e3fa45aaa4b45bb0ab3080e6\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\bd6d54c7e3fa45aaa4b45bb0ab3080e6\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\2ef59a3900bc6a0162f77e6c6369129b\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\2ef59a3900bc6a0162f77e6c6369129b\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\ae0358a2c93f2661aabc6c05923ecb92\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\ae0358a2c93f2661aabc6c05923ecb92\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\cac43e177d38c96eb682e0b63c61a4fe\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\cac43e177d38c96eb682e0b63c61a4fe\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\5d2c8e8fd1e35d7a1f7cc76443dff543\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\5d2c8e8fd1e35d7a1f7cc76443dff543\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a2597c41e05f7165bc731d5973035545\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a2597c41e05f7165bc731d5973035545\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\411fc5cafa49190fecc481318874d364\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\411fc5cafa49190fecc481318874d364\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\b12f5ec7860c79fe319ea01d980e4aac\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\b12f5ec7860c79fe319ea01d980e4aac\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\7109a23afcec01fecc6451570c536a6e\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\7109a23afcec01fecc6451570c536a6e\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\7ee2f5ae46b8b5ca8aca69086d262583\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\7ee2f5ae46b8b5ca8aca69086d262583\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\7ded0d3e29af577fc8ec3b647a40ef07\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\7ded0d3e29af577fc8ec3b647a40ef07\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\adbf5b882905ad8bd23ebd88774c480b\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\adbf5b882905ad8bd23ebd88774c480b\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\7ed66526869fe9508195749b4a76521f\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\7ed66526869fe9508195749b4a76521f\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\c3d60fc26ee2d9aa37847d5633efcdbb\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\c3d60fc26ee2d9aa37847d5633efcdbb\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\f8d9f7d29ffa753ad60497c889a22b79\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\f8d9f7d29ffa753ad60497c889a22b79\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\9890182d3e55a8b00f8ae4ebdd8fcd3c\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\9890182d3e55a8b00f8ae4ebdd8fcd3c\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\cde17cea5b6708a047d75e9329bc15c9\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.6] E:\android_development_cache\.gradle\caches\8.11.1\transforms\cde17cea5b6708a047d75e9329bc15c9\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\19126d6c5cae32eced731ac52afac268\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\19126d6c5cae32eced731ac52afac268\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a9868b39cc567603bcdb39fa45db6dee\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a9868b39cc567603bcdb39fa45db6dee\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\ffd9c8c98b99132324bc77dd786b1cb7\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\ffd9c8c98b99132324bc77dd786b1cb7\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\6cddd03f981b66f6053ec06c3046d7ce\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\6cddd03f981b66f6053ec06c3046d7ce\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\8e203b00f181f9efd9b72b0103240e53\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\8e203b00f181f9efd9b72b0103240e53\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\efeec16633ba2aa47bdc0240bc5612c4\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\efeec16633ba2aa47bdc0240bc5612c4\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e8868848eae7ccc45186e654e202dd67\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e8868848eae7ccc45186e654e202dd67\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\c025c2a7d9b0b6d0c82fa5b4b20e98fa\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\c025c2a7d9b0b6d0c82fa5b4b20e98fa\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] E:\android_development_cache\.gradle\caches\8.11.1\transforms\d0570d073c0340282798e2e43d0389b3\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] E:\android_development_cache\.gradle\caches\8.11.1\transforms\d0570d073c0340282798e2e43d0389b3\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\30db5274f68c630462674577ed3695b2\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\30db5274f68c630462674577ed3695b2\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\60bd08f5c0cca82d92c934f0df393e13\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\60bd08f5c0cca82d92c934f0df393e13\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\ed47751eec0b5bd9b7ecc2db0c215400\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\ed47751eec0b5bd9b7ecc2db0c215400\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\51dc201c90802576836ef22b82cc97d5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\51dc201c90802576836ef22b82cc97d5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\440420c1464a580f64d615cb46f54ca1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\440420c1464a580f64d615cb46f54ca1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\NAVIchora\Chora\app\src\main\AndroidManifest.xml
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.media3:media3-exoplayer:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\114a5d5301d7649ab21683cbabeecc89\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\366408bcdded224ef4b677da8c9e5d22\transformed\media3-common-1.7.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\366408bcdded224ef4b677da8c9e5d22\transformed\media3-common-1.7.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [androidx.media3:media3-exoplayer:1.7.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\114a5d5301d7649ab21683cbabeecc89\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:22:22-76
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\482f957f8861a0a617b384187dce0101\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\acf97b938bacc48adeb9122891289346\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e8868848eae7ccc45186e654e202dd67\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e8868848eae7ccc45186e654e202dd67\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\33c95c29660c3752a8577cb32349b727\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.craftworks.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.craftworks.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\14cf378553861cbecadb791c6e2341a9\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\e1ac2d19f4a8792143510de7bb6ba4c4\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.1] E:\android_development_cache\.gradle\caches\8.11.1\transforms\562ccceb3fd10dd2c4274d5aa39c3ff0\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:24:13-63
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] E:\android_development_cache\.gradle\caches\8.11.1\transforms\a78b645410f124a74c846825d0e1da92\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
